{"version": 3, "file": "registration_detail.js", "sources": ["pages_sub/pages_other/registration_detail.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX290aGVyXHJlZ2lzdHJhdGlvbl9kZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"registration-detail-page\">\r\n\t  <u-navbar\r\n\t        title=\"报名详情\"\r\n\t        :safeAreaInsetTop=\"true\"\r\n\t        :autoBack=\"true\"\r\n\t      >\r\n\t      </u-navbar>\r\n    <!-- 页面头部 -->\r\n    <view class=\"page-header\">\r\n      <view class=\"header-content\">\r\n        <text class=\"page-title\">报名详情</text>\r\n        <text class=\"event-title\">{{ eventTitle }}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载状态 -->\r\n    <view v-if=\"isLoading\" class=\"loading-container\">\r\n      <u-loading-icon mode=\"spinner\" size=\"40\" color=\"#007AFF\"></u-loading-icon>\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view>\r\n\r\n    <!-- 报名信息展示 -->\r\n    <view v-else-if=\"formConfig.length > 0 && submittedData\" class=\"form-container\">\r\n      <view class=\"form-section\">\r\n        <view class=\"section-title\">\r\n          <text>报名信息</text>\r\n        </view>\r\n        \r\n        <!-- 动态渲染表单项 -->\r\n        <view \r\n          v-for=\"item in formConfig\" \r\n          :key=\"item.field\" \r\n          class=\"form-item\"\r\n        >\r\n          <view class=\"item-label\">\r\n            <text>{{ item.label }}</text>\r\n            <text v-if=\"item.required\" class=\"required-mark\">*</text>\r\n          </view>\r\n          <view class=\"item-value\">\r\n            <!-- 根据不同类型展示不同格式的数据 -->\r\n            <text v-if=\"item.type === 'select' || item.type === 'radio'\">\r\n              {{ getDisplayValue(item, submittedData[item.field]) }}\r\n            </text>\r\n            <text v-else-if=\"item.type === 'checkbox'\">\r\n              {{ getCheckboxDisplayValue(item, submittedData[item.field]) }}\r\n            </text>\r\n            <text v-else-if=\"item.type === 'textarea'\">\r\n              {{ submittedData[item.field] || '未填写' }}\r\n            </text>\r\n            <text v-else>\r\n              {{ submittedData[item.field] || '未填写' }}\r\n            </text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 提交时间信息 -->\r\n<!--      <view v-if=\"registrationInfo\" class=\"info-section\">\r\n        <view class=\"section-title\">\r\n          <text>提交信息</text>\r\n        </view>\r\n        <view class=\"info-item\">\r\n          <text class=\"info-label\">提交时间：</text>\r\n          <text class=\"info-value\">{{ formatDate(registrationInfo.createTime) }}</text>\r\n        </view>\r\n        <view v-if=\"registrationInfo.updateTime && registrationInfo.updateTime !== registrationInfo.createTime\" class=\"info-item\">\r\n          <text class=\"info-label\">更新时间：</text>\r\n          <text class=\"info-value\">{{ formatDate(registrationInfo.updateTime) }}</text>\r\n        </view>\r\n      </view> -->\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view v-else class=\"empty-container\">\r\n      <text class=\"empty-text\">暂无报名信息</text>\r\n      <u-button \r\n        type=\"primary\" \r\n        size=\"normal\" \r\n        @click=\"goToRegistration\"\r\n        style=\"margin-top: 20rpx;\"\r\n      >\r\n        立即报名\r\n      </u-button>\r\n    </view>\r\n\t\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {ref} from 'vue'\r\nimport {onLoad} from '@dcloudio/uni-app'\r\nimport {getFormDefinitionApi, getMyRegistrationDetailApi} from '@/pages_sub/pages_profile/api/data/registration.js'\r\n\r\n// 响应式数据\r\nconst isLoading = ref(true)\r\nconst eventId = ref('')\r\nconst eventTitle = ref('')\r\nconst formConfig = ref([])\r\nconst submittedData = ref(null)\r\nconst registrationInfo = ref(null)\r\n\r\n// 页面加载\r\nonLoad(async (options) => {\r\n  eventId.value = options.eventId\r\n  eventTitle.value = decodeURIComponent(options.title || '活动详情')\r\n  \r\n  if (!eventId.value) {\r\n    uni.showToast({\r\n      title: '参数错误',\r\n      icon: 'error'\r\n    })\r\n    setTimeout(() => {\r\n      uni.navigateBack()\r\n    }, 1500)\r\n    return\r\n  }\r\n\r\n  await loadRegistrationDetail()\r\n})\r\n\r\n// 加载报名详情\r\nconst loadRegistrationDetail = async () => {\r\n  try {\r\n    isLoading.value = true\r\n    \r\n    console.log('开始加载报名详情，eventId:', eventId.value, '类型:', typeof eventId.value)\r\n    \r\n    // 并行请求表单定义和报名数据\r\n    const [formRes, detailRes] = await Promise.all([\r\n      getFormDefinitionApi(eventId.value),\r\n      getMyRegistrationDetailApi(eventId.value)\r\n    ])\r\n    \r\n    console.log('表单定义响应:', formRes)\r\n    console.log('报名详情响应:', detailRes)\r\n    \r\n    // 处理表单定义\r\n    if (formRes.code === 200 && formRes.data) {\r\n      // 🔧 修复：处理字符串形式的表单定义\r\n      let formDefinition;\r\n      if (typeof formRes.data === 'string') {\r\n        try {\r\n          formDefinition = JSON.parse(formRes.data);\r\n          console.log('解析后的表单定义:', formDefinition);\r\n        } catch (parseError) {\r\n          console.error('解析表单定义失败:', parseError);\r\n          throw new Error('表单配置格式错误');\r\n        }\r\n      } else {\r\n        formDefinition = formRes.data;\r\n      }\r\n      \r\n      // 获取字段数组\r\n      const fields = formDefinition.fields || [];\r\n      formConfig.value = Array.isArray(fields) ? fields : [];\r\n      console.log('最终表单配置:', formConfig.value);\r\n    } else {\r\n      console.warn('获取表单定义失败:', formRes);\r\n      throw new Error('获取表单定义失败');\r\n    }\r\n    \r\n    // 处理报名数据\r\n    if (detailRes.code === 200 && detailRes.data) {\r\n      console.log('报名数据结构:', detailRes.data);\r\n      \r\n      // 🔧 修复：处理不同的数据结构\r\n      let formData = {};\r\n      if (detailRes.data.formData) {\r\n        // 如果 formData 是字符串，需要解析\r\n        if (typeof detailRes.data.formData === 'string') {\r\n          try {\r\n            formData = JSON.parse(detailRes.data.formData);\r\n            console.log('解析后的表单数据:', formData);\r\n          } catch (parseError) {\r\n            console.error('解析表单数据失败:', parseError);\r\n            formData = {};\r\n          }\r\n        } else {\r\n          formData = detailRes.data.formData || {};\r\n        }\r\n      }\r\n      \r\n      submittedData.value = formData;\r\n      registrationInfo.value = detailRes.data;\r\n      console.log('最终显示数据:', {\r\n        submittedData: submittedData.value,\r\n        registrationInfo: registrationInfo.value\r\n      });\r\n    } else if (detailRes.code === 404) {\r\n      // 用户未报名\r\n      console.log('用户未报名，显示空状态');\r\n      formConfig.value = [];\r\n      submittedData.value = null;\r\n    } else {\r\n      console.warn('获取报名详情失败:', detailRes);\r\n      throw new Error(detailRes.msg || '获取报名详情失败');\r\n    }\r\n    \r\n  } catch (error) {\r\n    console.error('加载报名详情失败:', error)\r\n    uni.showToast({\r\n      title: error.message || '加载失败',\r\n      icon: 'error'\r\n    })\r\n  } finally {\r\n    isLoading.value = false\r\n  }\r\n}\r\n\r\n// 获取选择类型字段的显示值\r\nconst getDisplayValue = (item, value) => {\r\n  if (!value || !item.options) return '未选择'\r\n  \r\n  const option = item.options.find(opt => opt.value === value)\r\n  return option ? option.label : value\r\n}\r\n\r\n// 获取复选框类型字段的显示值\r\nconst getCheckboxDisplayValue = (item, value) => {\r\n  if (!value || !Array.isArray(value) || value.length === 0) return '未选择'\r\n  if (!item.options) return value.join(', ')\r\n  \r\n  const labels = value.map(val => {\r\n    const option = item.options.find(opt => opt.value === val)\r\n    return option ? option.label : val\r\n  })\r\n  \r\n  return labels.join(', ')\r\n}\r\n\r\n// 格式化日期\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return ''\r\n  \r\n  const date = new Date(dateString)\r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  \r\n  return `${year}-${month}-${day} ${hours}:${minutes}`\r\n}\r\n\r\n// 跳转到报名页面\r\nconst goToRegistration = () => {\r\n  uni.navigateTo({\r\n    url: `/pages/event/registration?id=${eventId.value}&title=${encodeURIComponent(eventTitle.value)}`\r\n  })\r\n}\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n  uni.navigateBack()\r\n}\r\n\r\n// 跳转到活动详情页\r\nconst goToEvent = () => {\r\n  uni.navigateTo({\r\n    url: `/pages/event/detail?id=${eventId.value}`\r\n  })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.registration-detail-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #f8f8f8;\r\n  padding-bottom: 120rpx;\r\n}\r\n\r\n.page-header {\r\n  background-color: #ffffff;\r\n  margin: 30rpx;\r\n  padding: 30rpx;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .header-content {\r\n    .page-title {\r\n      font-size: 36rpx;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      display: block;\r\n      margin-bottom: 10rpx;\r\n      text-align: center;\r\n    }\r\n    \r\n    .event-title {\r\n      font-size: 28rpx;\r\n      color: #666666;\r\n      display: block;\r\n      text-align: center;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n  \r\n  .loading-text {\r\n    margin-top: 20rpx;\r\n    color: #666;\r\n    font-size: 28rpx;\r\n  }\r\n}\r\n\r\n.form-container {\r\n  padding: 0 30rpx 30rpx 30rpx;\r\n}\r\n\r\n.form-section, .info-section {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  margin-bottom: 30rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  background-color: #f8f9fa;\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #eee;\r\n  \r\n  text {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333333;\r\n  }\r\n}\r\n\r\n.form-item {\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n  \r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n  \r\n  .item-label {\r\n    margin-bottom: 15rpx;\r\n    \r\n    text {\r\n      font-size: 28rpx;\r\n      color: #666666;\r\n      font-weight: 500;\r\n    }\r\n    \r\n    .required-mark {\r\n      color: #f56c6c;\r\n      margin-left: 5rpx;\r\n    }\r\n  }\r\n  \r\n  .item-value {\r\n    text {\r\n      font-size: 30rpx;\r\n      color: #333333;\r\n      line-height: 1.6;\r\n      word-break: break-all;\r\n    }\r\n  }\r\n}\r\n\r\n.info-item {\r\n  padding: 20rpx 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n  \r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n  \r\n  .info-label {\r\n    font-size: 28rpx;\r\n    color: #666666;\r\n    min-width: 160rpx;\r\n  }\r\n  \r\n  .info-value {\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n.empty-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 30rpx;\r\n  min-height: 60vh;\r\n  \r\n  .empty-text {\r\n    font-size: 32rpx;\r\n    color: #999999;\r\n    margin-bottom: 40rpx;\r\n  }\r\n}\r\n\r\n.bottom-actions {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: #ffffff;\r\n  padding: 30rpx;\r\n  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  align-items: center;\r\n  z-index: 100;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_other/registration_detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "getFormDefinitionApi", "getMyRegistrationDetailApi"], "mappings": ";;;;;;;;;;;;;;;;;;AA+FA,UAAM,YAAYA,cAAG,IAAC,IAAI;AAC1B,UAAM,UAAUA,cAAG,IAAC,EAAE;AACtB,UAAM,aAAaA,cAAG,IAAC,EAAE;AACzB,UAAM,aAAaA,cAAG,IAAC,EAAE;AACzB,UAAM,gBAAgBA,cAAG,IAAC,IAAI;AAC9B,UAAM,mBAAmBA,cAAG,IAAC,IAAI;AAGjCC,kBAAM,OAAC,OAAO,YAAY;AACxB,cAAQ,QAAQ,QAAQ;AACxB,iBAAW,QAAQ,mBAAmB,QAAQ,SAAS,MAAM;AAE7D,UAAI,CAAC,QAAQ,OAAO;AAClBC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,aAAc;AAAA,QACnB,GAAE,IAAI;AACP;AAAA,MACD;AAED,YAAM,uBAAwB;AAAA,IAChC,CAAC;AAGD,UAAM,yBAAyB,YAAY;AACzC,UAAI;AACF,kBAAU,QAAQ;AAElBA,sBAAAA,MAAA,MAAA,OAAA,wDAAY,qBAAqB,QAAQ,OAAO,OAAO,OAAO,QAAQ,KAAK;AAG3E,cAAM,CAAC,SAAS,SAAS,IAAI,MAAM,QAAQ,IAAI;AAAA,UAC7CC,8CAAoB,qBAAC,QAAQ,KAAK;AAAA,UAClCC,8CAA0B,2BAAC,QAAQ,KAAK;AAAA,QAC9C,CAAK;AAEDF,sBAAAA,MAAA,MAAA,OAAA,wDAAY,WAAW,OAAO;AAC9BA,sBAAAA,MAAY,MAAA,OAAA,wDAAA,WAAW,SAAS;AAGhC,YAAI,QAAQ,SAAS,OAAO,QAAQ,MAAM;AAExC,cAAI;AACJ,cAAI,OAAO,QAAQ,SAAS,UAAU;AACpC,gBAAI;AACF,+BAAiB,KAAK,MAAM,QAAQ,IAAI;AACxCA,4BAAA,MAAA,MAAA,OAAA,wDAAY,aAAa,cAAc;AAAA,YACxC,SAAQ,YAAY;AACnBA,yGAAc,aAAa,UAAU;AACrC,oBAAM,IAAI,MAAM,UAAU;AAAA,YAC3B;AAAA,UACT,OAAa;AACL,6BAAiB,QAAQ;AAAA,UAC1B;AAGD,gBAAM,SAAS,eAAe,UAAU;AACxC,qBAAW,QAAQ,MAAM,QAAQ,MAAM,IAAI,SAAS;AACpDA,wBAAY,MAAA,MAAA,OAAA,wDAAA,WAAW,WAAW,KAAK;AAAA,QAC7C,OAAW;AACLA,wBAAA,MAAA,MAAA,QAAA,wDAAa,aAAa,OAAO;AACjC,gBAAM,IAAI,MAAM,UAAU;AAAA,QAC3B;AAGD,YAAI,UAAU,SAAS,OAAO,UAAU,MAAM;AAC5CA,wBAAY,MAAA,MAAA,OAAA,wDAAA,WAAW,UAAU,IAAI;AAGrC,cAAI,WAAW,CAAA;AACf,cAAI,UAAU,KAAK,UAAU;AAE3B,gBAAI,OAAO,UAAU,KAAK,aAAa,UAAU;AAC/C,kBAAI;AACF,2BAAW,KAAK,MAAM,UAAU,KAAK,QAAQ;AAC7CA,8BAAA,MAAA,MAAA,OAAA,wDAAY,aAAa,QAAQ;AAAA,cAClC,SAAQ,YAAY;AACnBA,8BAAc,MAAA,MAAA,SAAA,wDAAA,aAAa,UAAU;AACrC,2BAAW,CAAA;AAAA,cACZ;AAAA,YACX,OAAe;AACL,yBAAW,UAAU,KAAK,YAAY,CAAA;AAAA,YACvC;AAAA,UACF;AAED,wBAAc,QAAQ;AACtB,2BAAiB,QAAQ,UAAU;AACnCA,wBAAAA,MAAA,MAAA,OAAA,wDAAY,WAAW;AAAA,YACrB,eAAe,cAAc;AAAA,YAC7B,kBAAkB,iBAAiB;AAAA,UAC3C,CAAO;AAAA,QACP,WAAe,UAAU,SAAS,KAAK;AAEjCA,wBAAAA,MAAY,MAAA,OAAA,wDAAA,aAAa;AACzB,qBAAW,QAAQ;AACnB,wBAAc,QAAQ;AAAA,QAC5B,OAAW;AACLA,wBAAA,MAAA,MAAA,QAAA,wDAAa,aAAa,SAAS;AACnC,gBAAM,IAAI,MAAM,UAAU,OAAO,UAAU;AAAA,QAC5C;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wDAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,MAAM,UAAU;AACvC,UAAI,CAAC,SAAS,CAAC,KAAK;AAAS,eAAO;AAEpC,YAAM,SAAS,KAAK,QAAQ,KAAK,SAAO,IAAI,UAAU,KAAK;AAC3D,aAAO,SAAS,OAAO,QAAQ;AAAA,IACjC;AAGA,UAAM,0BAA0B,CAAC,MAAM,UAAU;AAC/C,UAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW;AAAG,eAAO;AAClE,UAAI,CAAC,KAAK;AAAS,eAAO,MAAM,KAAK,IAAI;AAEzC,YAAM,SAAS,MAAM,IAAI,SAAO;AAC9B,cAAM,SAAS,KAAK,QAAQ,KAAK,SAAO,IAAI,UAAU,GAAG;AACzD,eAAO,SAAS,OAAO,QAAQ;AAAA,MACnC,CAAG;AAED,aAAO,OAAO,KAAK,IAAI;AAAA,IACzB;AAiBA,UAAM,mBAAmB,MAAM;AAC7BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gCAAgC,QAAQ,KAAK,UAAU,mBAAmB,WAAW,KAAK,CAAC;AAAA,MACpG,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzPA,GAAG,WAAW,eAAe;"}