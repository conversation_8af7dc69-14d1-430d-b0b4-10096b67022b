<template>
</template>

<script>

export default {
  onLaunch: function () {
    console.log('App Launch')

    // 预加载uview-plus图标字体
    this.loadIconFont()

    // 预加载阿里巴巴普惠体（不同字重），并输出控制台日志
    this.loadPuHuiTiFonts()
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },
  methods: {
    loadIconFont() {
      // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY
      uni.loadFontFace({
        global: true,
        family: 'uicon-iconfont',
        source: 'url("https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf")',
        success() {
          console.log('uview-plus图标字体加载成功');
        },
        fail(err) {
          console.error('uview-plus图标字体加载失败:', err);
          uni.loadFontFace({
            global: true,
            family: 'uicon-iconfont',
            source: 'url("https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf")',
            success() {
              console.log('备用图标字体加载成功');
            },
            fail(err2) {
              console.error('备用图标字体也加载失败:', err2);
            }
          });
        }
      });
      // #endif
    },

    // 动态加载阿里巴巴普惠体（统一使用公共CDN），控制台输出成败信息
    loadPuHuiTiFonts() {
      const PUHUITI_CDN_BASE = 'https://cdn.jsdelivr.net/npm/@alibabafonts/puhuiti@2.0.0/fonts'
      // const PUHUITI_CDN_BASE = 'https://wx.hongdashuiwu.com/fonts'
      if (!PUHUITI_CDN_BASE) {
        console.warn('[PuHuiTi] 需要 HTTPS 远程字体。已跳过动态加载。请配置 PUHUITI_CDN_BASE 并加入合法域名后再试。')
        return
      }
      const fontList = [
        { family: 'Alibaba PuHuiTi 3.0', weight: '400', url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Regular.woff2` },
        { family: 'Alibaba PuHuiTi 3.0', weight: '500', url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Medium.woff2` },
        { family: 'Alibaba PuHuiTi 3.0', weight: '700', url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Bold.woff2` },
        { family: 'Alibaba PuHuiTi 3.0', weight: '800', url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Heavy.woff2` }
      ]

      fontList.forEach(f => {
        // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY
        uni.loadFontFace({
          global: true,
          family: f.family,
          // 使用公共CDN远程 HTTPS 字体 URL
          source: `url("${f.url}")`,
          desc: { weight: f.weight, style: 'normal' },
          success() {
            console.log(`[PuHuiTi] 加载成功 -> family: ${f.family}, weight: ${f.weight}, url: ${f.url}`)
          },
          fail(err) {
            console.error(`[PuHuiTi] 加载失败 -> family: ${f.family}, weight: ${f.weight}, url: ${f.url}`, err)
          }
        })
        // #endif
      })
    }
  }
}
</script>

<style lang="scss">
/* 每个页面公共css */
@import "@/uni_modules/uview-plus/index.scss";

/* 防止页面跳转时内容残留 (这些样式很好，请保留) */
uni-page-wrapper {
  overflow: hidden !important;
}

uni-page-body {
  overflow: hidden !important;
}

  /* 小程序不允许本地字体文件，移除本地 @font-face 定义，统一走 loadPuHuiTiFonts 公共CDN 动态加载 */

/* 可选：为全局文本设置优先使用普惠体 */
body, page, view, text {
  font-family: 'Alibaba PuHuiTi 3.0', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica', 'sans-serif';
}
</style>