{"version": 3, "file": "EventInfoCard.js", "sources": ["components/event/EventInfoCard.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRJbmZvQ2FyZC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"info-card\">\r\n    <view class=\"card-header\">\r\n      <view :class=\"['status-tag-detail', getStatusClass(localEvent.status)]\">\r\n        <image class=\"status-bg-image\" src=\"/static/event/detail_bg.png\"></image>\r\n        <text class=\"status-text\">{{ formatEventStatus(localEvent.status) }}</text>\r\n      </view>\r\n      <view class=\"event-title-section\">\r\n        <text class=\"event-title\">{{ localEvent.title || '' }}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"info-row\">\r\n      <image class=\"info-icon\" src=\"/static/event/detail_icon_time.png\"></image>\r\n      <text class=\"info-text\">{{ formatEventTime }}</text>\r\n    </view>\r\n    <view class=\"info-row\">\r\n      <image class=\"info-icon\" src=\"/static/event/detail_icon_location.png\"></image>\r\n      <text class=\"info-text\">{{ localEvent.location || '' }}</text>\r\n    </view>\r\n    <view class=\"info-row\">\r\n      <image class=\"info-icon\" src=\"/static/event/detail_icon_user.png\"></image>\r\n      <rich-text :nodes=\"remainingSpotsNodes\" class=\"info-text\"></rich-text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { computed } from 'vue'\r\nimport { formatEventStatus, getStatusClass } from '@/utils/tools.js'\r\nimport { formatDate } from '@/utils/date.js'\r\n\r\nconst props = defineProps({\r\n  eventDetail: { type: Object, required: true }\r\n})\r\n\r\nconst localEvent = computed(() => props.eventDetail || {})\r\n\r\nconst formatEventTime = computed(() => {\r\n  if (!localEvent.value?.startTime || !localEvent.value?.endTime) {\r\n    return '时间待定'\r\n  }\r\n  try {\r\n    const startTime = formatDate(localEvent.value.startTime, 'YYYY-MM-DD HH:mm')\r\n    const endTime = formatDate(localEvent.value.endTime, 'YYYY-MM-DD HH:mm')\r\n    return `${startTime} 至 ${endTime}`\r\n  } catch (error) {\r\n    console.warn('时间格式化失败:', error)\r\n    return '时间格式错误'\r\n  }\r\n})\r\n\r\nconst remainingSpotsNodes = computed(() => {\r\n  if (!localEvent.value) {\r\n    return [{ type: 'text', text: '加载中...' }]\r\n  }\r\n\r\n  const max = Number(localEvent.value.maxParticipants) || 0\r\n  if (max === 0) {\r\n    return [{ type: 'text', text: '剩余名额: 不限人数' }]\r\n  }\r\n\r\n  const registered = Number(localEvent.value.registeredCount) || 0\r\n  const remaining = Math.max(0, max - registered)\r\n\r\n  return [\r\n    {\r\n      type: 'node',\r\n      name: 'span',\r\n      children: [\r\n        { type: 'text', text: '剩余名额: ' },\r\n        {\r\n          type: 'node',\r\n          name: 'span',\r\n          attrs: { style: 'color: #023F98;' },\r\n          children: [{ type: 'text', text: String(remaining) }]\r\n        },\r\n        { type: 'text', text: `/${max}` }\r\n      ]\r\n    }\r\n  ]\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info-card {\r\n  background: #FFFFFF;\r\n  margin: 0 30rpx;\r\n  margin-top: -60rpx;\r\n  border-radius: 16rpx 16rpx 16rpx 16rpx;\r\n  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2,63,152,0.1);\r\n  padding: 30rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.info-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.status-tag-detail {\r\n  width: 90rpx;\r\n  height: 40rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n  font-size: 22rpx;\r\n  border-radius: 12rpx;\r\n  margin-right: 16rpx;\r\n  margin-top: 15rpx;\r\n  flex-shrink: 0;\r\n  &.ended {\r\n    background-color: #909399;\r\n  }\r\n  &.ended .status-bg-image {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.event-title-section {\r\n  flex: 1;\r\n  min-width: 0;\r\n  margin-left: 16rpx;\r\n}\r\n\r\n.event-title {\r\n  white-space: normal;\r\n  word-break: break-word;\r\n  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\r\n  font-weight: normal;\r\n  font-size: 32rpx;\r\n  color: #23232A;\r\n  line-height: 1.5;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 24rpx;\r\n}\r\n\r\n.info-text {\r\n  font-family: \"Alibaba PuHuiTi 3.0\", \"Alibaba PuHuiTi 30\", sans-serif;\r\n  font-size: 26rpx;\r\n  color: #606266;\r\n  margin-left: 16rpx;\r\n}\r\n\r\n.status-bg-image {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1;\r\n}\r\n\r\n.status-text {\r\n  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\r\n  font-weight: normal;\r\n  font-size: 22rpx;\r\n  color: #023F98;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n</style>\r\n\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventInfoCard.vue'\nwx.createComponent(Component)"], "names": ["computed", "formatDate", "uni"], "mappings": ";;;;;;;;;;;AAgCA,UAAM,QAAQ;AAId,UAAM,aAAaA,cAAAA,SAAS,MAAM,MAAM,eAAe,CAAA,CAAE;AAEzD,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;;AACrC,UAAI,GAAC,gBAAW,UAAX,mBAAkB,cAAa,GAAC,gBAAW,UAAX,mBAAkB,UAAS;AAC9D,eAAO;AAAA,MACR;AACD,UAAI;AACF,cAAM,YAAYC,WAAAA,WAAW,WAAW,MAAM,WAAW,kBAAkB;AAC3E,cAAM,UAAUA,WAAAA,WAAW,WAAW,MAAM,SAAS,kBAAkB;AACvE,eAAO,GAAG,SAAS,MAAM,OAAO;AAAA,MACjC,SAAQ,OAAO;AACdC,sBAAAA,MAAA,MAAA,QAAA,4CAAa,YAAY,KAAK;AAC9B,eAAO;AAAA,MACR;AAAA,IACH,CAAC;AAED,UAAM,sBAAsBF,cAAQ,SAAC,MAAM;AACzC,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,SAAQ,CAAE;AAAA,MACzC;AAED,YAAM,MAAM,OAAO,WAAW,MAAM,eAAe,KAAK;AACxD,UAAI,QAAQ,GAAG;AACb,eAAO,CAAC,EAAE,MAAM,QAAQ,MAAM,aAAY,CAAE;AAAA,MAC7C;AAED,YAAM,aAAa,OAAO,WAAW,MAAM,eAAe,KAAK;AAC/D,YAAM,YAAY,KAAK,IAAI,GAAG,MAAM,UAAU;AAE9C,aAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,YACR,EAAE,MAAM,QAAQ,MAAM,SAAU;AAAA,YAChC;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,OAAO,EAAE,OAAO,kBAAmB;AAAA,cACnC,UAAU,CAAC,EAAE,MAAM,QAAQ,MAAM,OAAO,SAAS,GAAG;AAAA,YACrD;AAAA,YACD,EAAE,MAAM,QAAQ,MAAM,IAAI,GAAG,GAAI;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;AChFD,GAAG,gBAAgB,SAAS;"}