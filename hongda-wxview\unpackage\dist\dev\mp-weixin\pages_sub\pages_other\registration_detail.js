"use strict";
const common_vendor = require("../../common/vendor.js");
const pages_sub_pages_profile_api_data_registration = require("../pages_profile/api/data/registration.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_loading_icon2 = common_vendor.resolveComponent("u-loading-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_navbar2 + _easycom_u_loading_icon2 + _easycom_u_button2)();
}
const _easycom_u_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_loading_icon = () => "../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_navbar + _easycom_u_loading_icon + _easycom_u_button)();
}
const _sfc_main = {
  __name: "registration_detail",
  setup(__props) {
    const isLoading = common_vendor.ref(true);
    const eventId = common_vendor.ref("");
    const eventTitle = common_vendor.ref("");
    const formConfig = common_vendor.ref([]);
    const submittedData = common_vendor.ref(null);
    const registrationInfo = common_vendor.ref(null);
    common_vendor.onLoad(async (options) => {
      eventId.value = options.eventId;
      eventTitle.value = decodeURIComponent(options.title || "活动详情");
      if (!eventId.value) {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "error"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
        return;
      }
      await loadRegistrationDetail();
    });
    const loadRegistrationDetail = async () => {
      try {
        isLoading.value = true;
        common_vendor.index.__f__("log", "at pages_sub/pages_other/registration_detail.vue:127", "开始加载报名详情，eventId:", eventId.value, "类型:", typeof eventId.value);
        const [formRes, detailRes] = await Promise.all([
          pages_sub_pages_profile_api_data_registration.getFormDefinitionApi(eventId.value),
          pages_sub_pages_profile_api_data_registration.getMyRegistrationDetailApi(eventId.value)
        ]);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/registration_detail.vue:135", "表单定义响应:", formRes);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/registration_detail.vue:136", "报名详情响应:", detailRes);
        if (formRes.code === 200 && formRes.data) {
          let formDefinition;
          if (typeof formRes.data === "string") {
            try {
              formDefinition = JSON.parse(formRes.data);
              common_vendor.index.__f__("log", "at pages_sub/pages_other/registration_detail.vue:145", "解析后的表单定义:", formDefinition);
            } catch (parseError) {
              common_vendor.index.__f__("error", "at pages_sub/pages_other/registration_detail.vue:147", "解析表单定义失败:", parseError);
              throw new Error("表单配置格式错误");
            }
          } else {
            formDefinition = formRes.data;
          }
          const fields = formDefinition.fields || [];
          formConfig.value = Array.isArray(fields) ? fields : [];
          common_vendor.index.__f__("log", "at pages_sub/pages_other/registration_detail.vue:157", "最终表单配置:", formConfig.value);
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_other/registration_detail.vue:159", "获取表单定义失败:", formRes);
          throw new Error("获取表单定义失败");
        }
        if (detailRes.code === 200 && detailRes.data) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/registration_detail.vue:165", "报名数据结构:", detailRes.data);
          let formData = {};
          if (detailRes.data.formData) {
            if (typeof detailRes.data.formData === "string") {
              try {
                formData = JSON.parse(detailRes.data.formData);
                common_vendor.index.__f__("log", "at pages_sub/pages_other/registration_detail.vue:174", "解析后的表单数据:", formData);
              } catch (parseError) {
                common_vendor.index.__f__("error", "at pages_sub/pages_other/registration_detail.vue:176", "解析表单数据失败:", parseError);
                formData = {};
              }
            } else {
              formData = detailRes.data.formData || {};
            }
          }
          submittedData.value = formData;
          registrationInfo.value = detailRes.data;
          common_vendor.index.__f__("log", "at pages_sub/pages_other/registration_detail.vue:186", "最终显示数据:", {
            submittedData: submittedData.value,
            registrationInfo: registrationInfo.value
          });
        } else if (detailRes.code === 404) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/registration_detail.vue:192", "用户未报名，显示空状态");
          formConfig.value = [];
          submittedData.value = null;
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_other/registration_detail.vue:196", "获取报名详情失败:", detailRes);
          throw new Error(detailRes.msg || "获取报名详情失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/registration_detail.vue:201", "加载报名详情失败:", error);
        common_vendor.index.showToast({
          title: error.message || "加载失败",
          icon: "error"
        });
      } finally {
        isLoading.value = false;
      }
    };
    const getDisplayValue = (item, value) => {
      if (!value || !item.options)
        return "未选择";
      const option = item.options.find((opt) => opt.value === value);
      return option ? option.label : value;
    };
    const getCheckboxDisplayValue = (item, value) => {
      if (!value || !Array.isArray(value) || value.length === 0)
        return "未选择";
      if (!item.options)
        return value.join(", ");
      const labels = value.map((val) => {
        const option = item.options.find((opt) => opt.value === val);
        return option ? option.label : val;
      });
      return labels.join(", ");
    };
    const goToRegistration = () => {
      common_vendor.index.navigateTo({
        url: `/pages/event/registration?id=${eventId.value}&title=${encodeURIComponent(eventTitle.value)}`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "报名详情",
          safeAreaInsetTop: true,
          autoBack: true
        }),
        b: common_vendor.t(eventTitle.value),
        c: isLoading.value
      }, isLoading.value ? {
        d: common_vendor.p({
          mode: "spinner",
          size: "40",
          color: "#007AFF"
        })
      } : formConfig.value.length > 0 && submittedData.value ? {
        f: common_vendor.f(formConfig.value, (item, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.label),
            b: item.required
          }, item.required ? {} : {}, {
            c: item.type === "select" || item.type === "radio"
          }, item.type === "select" || item.type === "radio" ? {
            d: common_vendor.t(getDisplayValue(item, submittedData.value[item.field]))
          } : item.type === "checkbox" ? {
            f: common_vendor.t(getCheckboxDisplayValue(item, submittedData.value[item.field]))
          } : item.type === "textarea" ? {
            h: common_vendor.t(submittedData.value[item.field] || "未填写")
          } : {
            i: common_vendor.t(submittedData.value[item.field] || "未填写")
          }, {
            e: item.type === "checkbox",
            g: item.type === "textarea",
            j: item.field
          });
        })
      } : {
        g: common_vendor.o(goToRegistration),
        h: common_vendor.p({
          type: "primary",
          size: "normal"
        })
      }, {
        e: formConfig.value.length > 0 && submittedData.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-05166276"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_other/registration_detail.js.map
