"use strict";
const development = {
  baseUrl: "http://localhost:80",
  // 开发环境API基础URL
  imageBaseUrl: "http://localhost:80",
  // 图片服务器基础URL
  timeout: 1e4,
  // 请求超时时间
  debug: true
  // 是否开启调试模式
};
const config = development;
const API_PATHS = {
  EVENT_LIST: "/events/list",
  EVENT_DETAIL: "/events",
  EVENT_EXPORT: "/content/pages_event/export",
  EVENT_LOCATIONS: "/events/locations",
  EVENT_HOT: "/events/hot",
  EVENT_UPCOMING: "/events/upcoming",
  EVENT_SEARCH: "/events/search",
  EVENT_BY_LOCATION: "/events/by-location",
  EVENT_LOCATION_STATS: "/events/location-stats"
};
const PAGE_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 50
};
const BASE_URL = config.baseUrl;
const IMAGE_BASE_URL = config.imageBaseUrl;
exports.API_PATHS = API_PATHS;
exports.BASE_URL = BASE_URL;
exports.IMAGE_BASE_URL = IMAGE_BASE_URL;
exports.PAGE_CONFIG = PAGE_CONFIG;
exports.config = config;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/config.js.map
