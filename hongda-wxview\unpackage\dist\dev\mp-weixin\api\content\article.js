"use strict";
const utils_request = require("../../utils/request.js");
function getArticleList(params) {
  const processedParams = {
    ...params
  };
  if (!processedParams.tagIds) {
    delete processedParams.tagIds;
  }
  if (!processedParams.title || processedParams.title.trim() === "") {
    delete processedParams.title;
  }
  return utils_request.get("/content/articles", processedParams);
}
function getArticleDetail(id) {
  return utils_request.get(`/content/article/${id}`);
}
exports.getArticleDetail = getArticleDetail;
exports.getArticleList = getArticleList;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/content/article.js.map
