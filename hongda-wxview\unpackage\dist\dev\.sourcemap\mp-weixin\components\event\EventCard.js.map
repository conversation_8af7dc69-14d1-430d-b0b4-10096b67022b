{"version": 3, "file": "EventCard.js", "sources": ["components/event/EventCard.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRDYXJkLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"event-card\" @click=\"$emit('click', event)\">\r\n    <view class=\"card-left\">\r\n      <image :src=\"getFullImageUrl(event.coverImageUrl)\" mode=\"aspectFill\" class=\"event-image\" :lazy-load=\"true\"></image>\r\n      <view v-if=\"Number(event.status) === 1 || Number(event.status) === 2\" :class=\"['status-tag', getStatusClass(event.status)]\">\r\n        {{ formatEventStatus(event.status) }}\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"card-right\">\r\n      <text class=\"event-title\">{{ event.title }}</text>\r\n\r\n      <view class=\"event-info-row\">\r\n        <view class=\"time-location-item\">\r\n          <image class=\"event-info-icon\" src=\"/static/event/时间icon灰@2x.png\" mode=\"aspectFit\"></image>\r\n          <text class=\"info-text\">{{ formatEventDate(event.startTime) }}</text>\r\n        </view>\r\n        <view class=\"time-location-item\">\r\n          <image class=\"event-info-icon\" src=\"/static/event/位置icon灰@2x.png\" mode=\"aspectFit\"></image>\r\n          <text class=\"info-text\">{{ formatEventLocation(event) }}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"event-info remaining-spots\">\r\n        <text class=\"spots-count\">\r\n          剩余名额: {{ calculateRemainingSpots(event.maxParticipants, event.registeredCount) }}\r\n        </text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n  \r\n</template>\r\n\r\n<script setup>\r\nimport { formatEventStatus, getStatusClass, calculateRemainingSpots } from '@/utils/tools.js'\r\nimport { formatEventDate } from '@/utils/date.js'\r\nimport { getFullImageUrl } from '@/utils/image.js'\r\n\r\nconst props = defineProps({\r\n  event: { type: Object, required: true }\r\n})\r\n\r\nconst formatEventLocation = (event) => {\r\n  if (event.city && event.city.trim()) {\r\n    return event.city.trim().replace(/市$/, '')\r\n  }\r\n  return '待定'\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.event-card {\r\n  width: 100%;\r\n  height: 272rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 0rpx 0rpx 0rpx 0rpx;\r\n  border: none;\r\n  border-top: 2rpx solid #EEEEEE;\r\n  border-bottom: 2rpx solid #EEEEEE;\r\n  margin-bottom: 0rpx;\r\n  padding: 24rpx 24rpx;\r\n  display: flex;\r\n  overflow: hidden;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.card-left {\r\n  position: relative;\r\n  width: 336rpx;\r\n  height: 192rpx;\r\n  flex-shrink: 0;\r\n  margin-top: 16rpx;\r\n  margin-bottom: 16rpx; \r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.event-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: block;\r\n  border-radius: 16rpx;\r\n}\r\n\r\n.status-tag {\r\n  position: absolute;\r\n  top: 12rpx;\r\n  left: 12rpx;\r\n  width: 90rpx;\r\n  height: 40rpx;\r\n  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\r\n  border-radius: 20rpx 20rpx 20rpx 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-sizing: border-box;\r\n\r\n  color: #23232A;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n  font-weight: normal;\r\n  font-size: 22rpx;\r\n  text-align: left;\r\n  font-style: normal;\r\n  text-transform: none;\r\n  line-height: 1;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n\r\n  &.ended {\r\n    background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);\r\n  }\r\n}\r\n\r\n.card-right {\r\n  flex: 1;\r\n  padding: 16rpx 20rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.event-title {\r\n  width: 346rpx;\r\n  height: 80rpx;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n  font-weight: normal;\r\n  font-size: 28rpx;\r\n  color: #23232A;\r\n  text-align: justify;\r\n  font-style: normal;\r\n  text-transform: none;\r\n  line-height: 1.4;\r\n  margin-bottom: 24rpx;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  line-clamp: 2;\r\n  -webkit-line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.event-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.event-info-row {\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  justify-content: flex-start !important;\r\n  gap: 24rpx !important;\r\n  margin-bottom: 18rpx !important;\r\n  flex-wrap: nowrap !important;\r\n}\r\n\r\n.time-location-item {\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  gap: 8rpx !important;\r\n  flex-shrink: 0 !important;\r\n}\r\n\r\n.event-info-icon {\r\n  width: 32rpx !important;\r\n  height: 32rpx !important;\r\n  flex-shrink: 0 !important;\r\n}\r\n\r\n.info-text {\r\n  width: 176rpx !important;\r\n  height: 32rpx !important;\r\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;\r\n  font-weight: normal !important;\r\n  font-size: 22rpx !important;\r\n  color: #9B9A9A !important;\r\n  text-align: left !important;\r\n  font-style: normal !important;\r\n  text-transform: none !important;\r\n  line-height: 32rpx !important;\r\n  overflow: hidden !important;\r\n  text-overflow: ellipsis !important;\r\n  white-space: nowrap !important;\r\n}\r\n\r\n.remaining-spots {\r\n  width: 154rpx;\r\n  height: 40rpx;\r\n  border-radius: 4rpx 4rpx 4rpx 4rpx;\r\n  border: 1rpx solid #FB8620;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0;\r\n  margin: 0;\r\n  box-sizing: border-box;\r\n  overflow: hidden;\r\n  flex-shrink: 0;\r\n\r\n  .spots-count {\r\n    width: 100%;\r\n    height: 36rpx;\r\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n    font-weight: normal;\r\n    font-size: 20rpx;\r\n    color: #FB8620;\r\n    text-align: center;\r\n    font-style: normal;\r\n    text-transform: none;\r\n    line-height: 36rpx;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n  }\r\n}\r\n</style>\r\n\r\n\r\n\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventCard.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;;;;;;;;;;;AA0CA,UAAM,sBAAsB,CAAC,UAAU;AACrC,UAAI,MAAM,QAAQ,MAAM,KAAK,KAAI,GAAI;AACnC,eAAO,MAAM,KAAK,KAAM,EAAC,QAAQ,MAAM,EAAE;AAAA,MAC1C;AACD,aAAO;AAAA,IACT;;;;;;;;;;;;;;;;;;;;;AC9CA,GAAG,gBAAgB,SAAS;"}