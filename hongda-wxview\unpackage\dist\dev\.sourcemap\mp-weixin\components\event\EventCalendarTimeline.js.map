{"version": 3, "file": "EventCalendarTimeline.js", "sources": ["components/event/EventCalendarTimeline.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvZXZlbnQvRXZlbnRDYWxlbmRhclRpbWVsaW5lLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"calendar-timeline-wrapper\">\r\n    <view class=\"vertical-timeline\">\r\n        <view class=\"corner-notch\" :style=\"{ left: notchLeft }\" />\r\n      <view v-for=\"(group, index) in groups\" :key=\"group.date\" class=\"date-section\">\r\n        <view class=\"date-header\">\r\n          <view class=\"timeline-dot\"></view>\r\n          <text class=\"time-text\">{{ group.formattedDate }}</text>\r\n          <text class=\"weekday-text\">{{ group.dayOfWeek }}</text>\r\n        </view>\r\n\r\n        <view class=\"line-connector\" v-if=\"index < groups.length - 1\">\r\n          <view class=\"timeline-line\"></view>\r\n        </view>\r\n\r\n        <view class=\"events-container\">\r\n          <view\r\n            v-for=\"event in group.events\"\r\n            :key=\"event.id\"\r\n            class=\"compact-event-card\"\r\n            @click=\"$emit('click-item', event)\"\r\n          >\r\n            <image :src=\"getFullImageUrl(event.iconUrl)\" class=\"event-avatar\" mode=\"aspectFill\" />\r\n            <view class=\"event-content\">\r\n              <text class=\"event-title-compact\">{{ event.title }}</text>\r\n              <view class=\"location-group\">\r\n                <view class=\"separator-line\"></view>\r\n                <view class=\"event-location-compact\">\r\n                  <image class=\"location-icon\" src=\"/static/event/golden-location-icon.png\" mode=\"aspectFit\" />\r\n                  <text class=\"location-text\">{{ formatEventLocation(event) }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view v-if=\"hasMore\" class=\"no-more-divider\" @click=\"$emit('view-more')\">\r\n      <text class=\"no-more-text\">查看更多</text>\r\n    </view>\r\n  </view>\r\n  <view class=\"timeline-bottom-spacer\"></view>\r\n</template>\r\n  \r\n  <script setup>\r\n  import { getFullImageUrl } from '@/utils/image.js'\r\n  \r\n  defineProps({\r\n    groups: { type: Array, required: true },\r\n    hasMore: { type: Boolean, default: false },\r\n    notchLeft: { type: String, default: '60rpx' }\r\n  })\r\n  \r\n  const formatEventLocation = (event) => {\r\n    if (event.city && event.city.trim()) {\r\n      return event.city.trim().replace(/市$/, '')\r\n    }\r\n    return '待定'\r\n  }\r\n  </script>\r\n  \r\n  <style lang=\"scss\" scoped>\r\n.calendar-timeline-wrapper {\r\n  width: 100%;\r\n}\r\n  .vertical-timeline {\r\n    width: 702rpx;\r\n    background: #F0F2F3;\r\n    border-radius: 32rpx;\r\n    margin: 0 24rpx;\r\n    margin-top: 37rpx; \r\n    padding: 16rpx 0 20rpx 0;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    min-height: 200rpx; \r\n    overflow: visible;\r\n  }\r\n\r\n  /* 左上角凸起的角 */\r\n  .corner-notch {\r\n    position: absolute;\r\n    top: -16rpx; \r\n    left: 60rpx; \r\n    width: 0;\r\n    height: 0;\r\n    border-left: 14rpx solid transparent;\r\n    border-right: 14rpx solid transparent;\r\n    border-bottom: 16rpx solid #F0F2F3; \r\n    z-index: 103; \r\n  }\r\n  \r\n    .date-section {\r\n    position: relative;\r\n    padding-left: 72rpx;\r\n\r\n    &:first-child {\r\n      margin-top: 0;\r\n    }\r\n\r\n    &:not(:last-child) {\r\n      margin-bottom: 24rpx;\r\n    }\r\n  }\r\n  \r\n  .date-header {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    margin-bottom: 32rpx;\r\n  \r\n    .timeline-dot {\r\n      position: absolute;\r\n      left: 24rpx;\r\n      top: 13rpx;\r\n      width: 18rpx;\r\n      height: 18rpx;\r\n      background: #FFFFFF;\r\n      border: 2rpx solid #023F98;\r\n      border-radius: 50%;\r\n      z-index: 2;\r\n    }\r\n  \r\n    .time-text {\r\n      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n      font-size: 32rpx;\r\n      color: #023F98;\r\n      line-height: 44rpx;\r\n      font-weight: normal;\r\n    }\r\n  \r\n    .weekday-text {\r\n      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n      font-size: 22rpx;\r\n      color: #66666E;\r\n      line-height: 44rpx;\r\n      font-weight: normal;\r\n      margin-left: 15rpx;\r\n    }\r\n  }\r\n  \r\n  .line-connector {\r\n    position: absolute;\r\n    left: 32rpx;\r\n    top: 44rpx;\r\n    bottom: -197rpx;\r\n    width: 2rpx;\r\n  \r\n    .timeline-line {\r\n      height: 100%;\r\n      width: 100%;\r\n      background: #023F98;\r\n    }\r\n  }\r\n  \r\n  .date-section:last-child .line-connector .timeline-line {\r\n    display: none;\r\n  }\r\n  \r\n  .events-container {\r\n    .compact-event-card {\r\n      width: 590rpx;\r\n      height: 100rpx;\r\n      background: #FFFFFF;\r\n      border: 2rpx solid #023F98;\r\n      border-radius: 16rpx;\r\n      padding: 0 24rpx;\r\n      margin-bottom: 20rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12rpx;\r\n      transition: transform 0.2s ease;\r\n      box-sizing: border-box;\r\n  \r\n      &:last-child { margin-bottom: 0; }\r\n      &:active { transform: scale(0.98); }\r\n  \r\n      .event-avatar {\r\n        width: 52rpx;\r\n        height: 52rpx;\r\n        border-radius: 50%;\r\n        flex-shrink: 0;\r\n        background-color: #f5f5f5;\r\n      }\r\n  \r\n      .event-content {\r\n        flex: 1;\r\n        min-width: 0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-start;\r\n  \r\n        .event-title-compact {\r\n          font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n          font-size: 28rpx;\r\n          color: #23232A;\r\n          font-weight: normal;\r\n          flex: 1;\r\n          min-width: 0;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          line-height: 1.2;\r\n          margin-right: 24rpx;\r\n        }\r\n  \r\n        .location-group {\r\n          display: flex;\r\n          align-items: center;\r\n          flex-shrink: 0;\r\n        }\r\n  \r\n        .separator-line {\r\n          width: 2rpx;\r\n          height: 40rpx;\r\n          background: #FA841C;\r\n          opacity: 0.3;\r\n          flex-shrink: 0;\r\n          margin-right: 24rpx;\r\n        }\r\n  \r\n        .event-location-compact {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          justify-content: center;\r\n          flex-shrink: 0;\r\n          gap: 4rpx;\r\n  \r\n          .location-icon {\r\n            width: 32rpx;\r\n            height: 32rpx;\r\n            flex-shrink: 0;\r\n          }\r\n  \r\n          .location-text {\r\n            font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n            font-size: 22rpx;\r\n            color: #452D03;\r\n            font-weight: normal;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            line-height: 1.2;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .no-more-divider {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 24rpx 0;\r\n    margin-top: 60rpx;\r\n  }\r\n\r\n  .no-more-text {\r\n    width: 120rpx;\r\n    height: 34rpx;\r\n    line-height: 34rpx;\r\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\r\n    font-weight: normal;\r\n    font-size: 24rpx;\r\n    color: #9B9A9A;\r\n    text-align: center;\r\n    font-style: normal;\r\n    text-transform: none;\r\n  }\r\n\r\n  /* 底部占位 */\r\n  .timeline-bottom-spacer {\r\n    height: 220rpx; \r\n  }\r\n  </style>", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/event/EventCalendarTimeline.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;;;;;;;;;;;AAsDE,UAAM,sBAAsB,CAAC,UAAU;AACrC,UAAI,MAAM,QAAQ,MAAM,KAAK,KAAI,GAAI;AACnC,eAAO,MAAM,KAAK,KAAM,EAAC,QAAQ,MAAM,EAAE;AAAA,MAC1C;AACD,aAAO;AAAA,IACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1DH,GAAG,gBAAgB,SAAS;"}