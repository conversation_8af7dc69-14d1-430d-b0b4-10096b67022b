{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<template>\n</template>\n\n<script>\n\nexport default {\n  onLaunch: function () {\n    console.log('App Launch')\n\n    // 预加载uview-plus图标字体\n    this.loadIconFont()\n\n    // 预加载阿里巴巴普惠体（不同字重），并输出控制台日志\n    this.loadPuHuiTiFonts()\n  },\n  onShow: function () {\n    console.log('App Show')\n  },\n  onHide: function () {\n    console.log('App Hide')\n  },\n  methods: {\n    loadIconFont() {\n      // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY\n      uni.loadFontFace({\n        global: true,\n        family: 'uicon-iconfont',\n        source: 'url(\"https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf\")',\n        success() {\n          console.log('uview-plus图标字体加载成功');\n        },\n        fail(err) {\n          console.error('uview-plus图标字体加载失败:', err);\n          uni.loadFontFace({\n            global: true,\n            family: 'uicon-iconfont',\n            source: 'url(\"https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf\")',\n            success() {\n              console.log('备用图标字体加载成功');\n            },\n            fail(err2) {\n              console.error('备用图标字体也加载失败:', err2);\n            }\n          });\n        }\n      });\n      // #endif\n    },\n\n    // 动态加载阿里巴巴普惠体（统一使用公共CDN），控制台输出成败信息\n    loadPuHuiTiFonts() {\n      const PUHUITI_CDN_BASE = 'https://cdn.jsdelivr.net/npm/@alibabafonts/puhuiti@2.0.0/fonts'\n      // const PUHUITI_CDN_BASE = 'https://wx.hongdashuiwu.com/fonts'\n      if (!PUHUITI_CDN_BASE) {\n        console.warn('[PuHuiTi] 需要 HTTPS 远程字体。已跳过动态加载。请配置 PUHUITI_CDN_BASE 并加入合法域名后再试。')\n        return\n      }\n      const fontList = [\n        { family: 'Alibaba PuHuiTi 3.0', weight: '400', url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Regular.woff2` },\n        { family: 'Alibaba PuHuiTi 3.0', weight: '500', url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Medium.woff2` },\n        { family: 'Alibaba PuHuiTi 3.0', weight: '700', url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Bold.woff2` },\n        { family: 'Alibaba PuHuiTi 3.0', weight: '800', url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Heavy.woff2` }\n      ]\n\n      fontList.forEach(f => {\n        // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY\n        uni.loadFontFace({\n          global: true,\n          family: f.family,\n          // 使用公共CDN远程 HTTPS 字体 URL\n          source: `url(\"${f.url}\")`,\n          desc: { weight: f.weight, style: 'normal' },\n          success() {\n            console.log(`[PuHuiTi] 加载成功 -> family: ${f.family}, weight: ${f.weight}, url: ${f.url}`)\n          },\n          fail(err) {\n            console.error(`[PuHuiTi] 加载失败 -> family: ${f.family}, weight: ${f.weight}, url: ${f.url}`, err)\n          }\n        })\n        // #endif\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n/* 每个页面公共css */\n@import \"@/uni_modules/uview-plus/index.scss\";\n\n/* 防止页面跳转时内容残留 (这些样式很好，请保留) */\nuni-page-wrapper {\n  overflow: hidden !important;\n}\n\nuni-page-body {\n  overflow: hidden !important;\n}\n\n  /* 小程序不允许本地字体文件，移除本地 @font-face 定义，统一走 loadPuHuiTiFonts 公共CDN 动态加载 */\n\n/* 可选：为全局文本设置优先使用普惠体 */\nbody, page, view, text {\n  font-family: 'Alibaba PuHuiTi 3.0', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica', 'sans-serif';\n}\n</style>", "import App from './App'\r\n\r\n// #ifndef VUE3\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n\t...App\r\n})\r\napp.$mount()\r\n// #endif\r\n\r\n// #ifdef VUE3\r\nimport { createSSRApp } from 'vue'\r\nimport uviewPlus from '@/uni_modules/uview-plus'\r\n\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\r\n\t// 保留您原有的 uview-plus 插件配置\r\n\tapp.use(uviewPlus, () => {\r\n\t\treturn {\r\n\t\t\toptions: {\r\n\t\t\t\tconfig: {\r\n\t\t\t\t\tunit: 'rpx'\r\n\t\t\t\t},\r\n\t\t\t\tprops: {\r\n\t\t\t\t\t// ...\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}\r\n// #endif"], "names": ["uni", "createSSRApp", "uviewPlus"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAKA,MAAK,YAAU;AAAA,EACb,UAAU,WAAY;AACpBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,YAAY;AAGxB,SAAK,aAAa;AAGlB,SAAK,iBAAiB;AAAA,EACvB;AAAA,EACD,QAAQ,WAAY;AAClBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACvB;AAAA,EACD,QAAQ,WAAY;AAClBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,EACvB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AAEbA,oBAAAA,MAAI,aAAa;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AACRA,wBAAAA,MAAA,MAAA,OAAA,iBAAY,oBAAoB;AAAA,QACjC;AAAA,QACD,KAAK,KAAK;AACRA,wBAAA,MAAA,MAAA,SAAA,iBAAc,uBAAuB,GAAG;AACxCA,wBAAAA,MAAI,aAAa;AAAA,YACf,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,UAAU;AACRA,4BAAAA,MAAY,MAAA,OAAA,iBAAA,YAAY;AAAA,YACzB;AAAA,YACD,KAAK,MAAM;AACTA,4BAAA,MAAA,MAAA,SAAA,iBAAc,gBAAgB,IAAI;AAAA,YACpC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IAEF;AAAA;AAAA,IAGD,mBAAmB;AACjB,YAAM,mBAAmB;AAMzB,YAAM,WAAW;AAAA,QACf,EAAE,QAAQ,uBAAuB,QAAQ,OAAO,KAAK,GAAG,gBAAgB,iCAAkC;AAAA,QAC1G,EAAE,QAAQ,uBAAuB,QAAQ,OAAO,KAAK,GAAG,gBAAgB,gCAAiC;AAAA,QACzG,EAAE,QAAQ,uBAAuB,QAAQ,OAAO,KAAK,GAAG,gBAAgB,8BAA+B;AAAA,QACvG,EAAE,QAAQ,uBAAuB,QAAQ,OAAO,KAAK,GAAG,gBAAgB,+BAA+B;AAAA,MACzG;AAEA,eAAS,QAAQ,OAAK;AAEpBA,sBAAAA,MAAI,aAAa;AAAA,UACf,QAAQ;AAAA,UACR,QAAQ,EAAE;AAAA;AAAA,UAEV,QAAQ,QAAQ,EAAE,GAAG;AAAA,UACrB,MAAM,EAAE,QAAQ,EAAE,QAAQ,OAAO,SAAU;AAAA,UAC3C,UAAU;AACRA,8DAAY,6BAA6B,EAAE,MAAM,aAAa,EAAE,MAAM,UAAU,EAAE,GAAG,EAAE;AAAA,UACxF;AAAA,UACD,KAAK,KAAK;AACRA,0BAAc,MAAA,MAAA,SAAA,iBAAA,6BAA6B,EAAE,MAAM,aAAa,EAAE,MAAM,UAAU,EAAE,GAAG,IAAI,GAAG;AAAA,UAChG;AAAA,SACD;AAAA,OAEF;AAAA,IACH;AAAA,EACF;AACF;;;;;AClEO,SAAS,YAAY;AAC3B,QAAM,MAAMC,cAAY,aAAC,GAAG;AAG5B,MAAI,IAAIC,4BAAAA,WAAW,MAAM;AACxB,WAAO;AAAA,MACN,SAAS;AAAA,QACR,QAAQ;AAAA,UACP,MAAM;AAAA,QACN;AAAA,QACD,OAAO;AAAA;AAAA,QAEN;AAAA,MACD;AAAA,IACD;AAAA,EACH,CAAE;AAED,SAAO;AAAA,IACN;AAAA,EACA;AACF;;;"}