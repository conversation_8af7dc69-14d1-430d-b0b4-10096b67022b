"use strict";
const utils_request = require("../../../../utils/request.js");
function getCommentList(params) {
  return utils_request.http.get("/comment/list", params);
}
function addComment(data) {
  return utils_request.http.post("/comment/add", data);
}
exports.addComment = addComment;
exports.getCommentList = getCommentList;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages_sub/pages_article/api/content/comment.js.map
