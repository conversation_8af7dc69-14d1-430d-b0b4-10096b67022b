{"version": 3, "file": "orders.js", "sources": ["pages_sub/pages_profile/orders.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX3Byb2ZpbGVcb3JkZXJzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"orders-page\">\n    <!-- 顶部导航栏 -->\n    <up-navbar \n      title=\"报名订单\" \n        :autoBack=\"true\" \n        :fixed=\"true\" \n        :safeAreaInsetTop=\"true\" \n        :placeholder=\"true\"\n        bgColor=\"#ffffff\" \n        leftIcon=\"arrow-left\" \n        leftIconColor=\"#333333\"\n        titleStyle=\"font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30'; font-weight: normal; font-size: 32rpx; color: #000000; line-height: 44rpx; text-align: center; font-style: normal; text-transform: none;\"\n    />\n    \n    <!-- 主内容区域 -->\n    <scroll-view \n      scroll-y \n      class=\"orders-list-scroll\" \n      @scrolltolower=\"onLoadMore\"\n      refresher-enabled \n      :refresher-triggered=\"isRefreshing\" \n      @refresherrefresh=\"onRefresh\"\n    >\n      <!-- 加载状态 -->\n      <view v-if=\"loading && registrationList.length === 0\" class=\"loading-state\">\n        <up-loading-page :loading=\"true\" loading-text=\"加载中...\" />\n      </view>\n      \n      <!-- 空状态 -->\n      <view v-else-if=\"!loading && registrationList.length === 0\" class=\"empty-state\">\n        <up-empty \n          mode=\"list\" \n          :text=\"isLoggedIn ? '暂无报名记录' : '请先登录查看报名记录'\" \n          textColor=\"#909399\" \n          iconSize=\"120\"\n          icon=\"/static/placeholder.png\"\n        >\n          <template #bottom v-if=\"!isLoggedIn\">\n            <up-button type=\"primary\" text=\"去登录\" @click=\"goToLogin\" />\n          </template>\n        </up-empty>\n      </view>\n      \n      <!-- 报名订单列表 -->\n      <view v-else class=\"orders-list\">\n        <view\n          v-for=\"item in registrationList\"\n          :key=\"item.id\"\n          class=\"order-card\"\n          @click=\"goToEventDetail(item.eventId)\"\n        >\n          <!-- 左侧：活动封面 -->\n          <view class=\"card-left\">\n            <image\n              :src=\"item.coverImageUrl || getFullImageUrl('/static/placeholder.png')\"\n              mode=\"aspectFill\"\n              class=\"event-image\"\n              :lazy-load=\"true\"\n              @error=\"onImageError\"\n              @load=\"onImageLoad\"\n            />\n          </view>\n\n          <!-- 右侧：活动信息 -->\n          <view class=\"card-right\">\n            <!-- 活动标题 -->\n            <text class=\"event-title\">{{ item.title || '活动标题' }}</text>\n\n            <!-- 报名时间 -->\n            <view class=\"event-info registration-time\">\n              <text class=\"time-label\">报名时间：</text>\n              <text class=\"time-value\">{{ formatRegistrationTime(item.registrationTime) }}</text>\n            </view>\n\n            <!-- 底部行：只显示状态标签 -->\n            <view class=\"card-bottom-row\">\n              <view>\n                <!-- 已报名状态使用背景图片 -->\n                <view\n                  v-if=\"item.status === 0\"\n                  class=\"status-with-bg\"\n                >\n                  <image\n                    class=\"status-bg-image\"\n                    src=\"/static/profile/order_bg.png\"\n                    mode=\"aspectFit\"\n                  ></image>\n                  <text class=\"status-text\">已报名</text>\n                </view>\n                <!-- 已取消状态使用灰色背景 -->\n                <view\n                  v-else-if=\"item.status === 1\"\n                  class=\"status-cancelled\"\n                >\n                  <text class=\"status-text\">已取消</text>\n                </view>\n                <!-- 其他状态使用普通标签 -->\n                <view\n                  v-else\n                  :class=\"['registration-status-tag', getRegistrationStatusClass(item.status)]\"\n                >\n                  {{ formatRegistrationStatus(item.status) }}\n                </view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 独立的取消报名按钮 - 基于卡片容器绝对定位 -->\n          <view\n            v-if=\"item.status === 0\"\n            class=\"cancel-btn-absolute\"\n            @click.stop=\"showCancelConfirm(item)\"\n          >\n            取消报名\n          </view>\n        </view>\n      </view>\n      \n      <!-- 加载更多组件 -->\n      <view class=\"loadmore-wrapper\" v-if=\"registrationList.length > 0\">\n        <up-loadmore \n          :status=\"loadMoreStatus\" \n          :loading-text=\"'正在加载...'\" \n          :loadmore-text=\"'上拉加载更多'\"\n          :nomore-text=\"'没有更多了'\" \n        />\n      </view>\n    </scroll-view>\n    \n    <!-- 自定义底部导航栏 -->\n    <CustomTabBar :current=\"4\" />\n\n    <!-- 自定义取消报名确认弹窗 -->\n    <view v-if=\"showCancelModal\" class=\"cancel-modal-overlay\" @click=\"closeCancelModal\">\n      <view class=\"cancel-modal-content\" @click.stop>\n        <!-- 警告图标和标题 -->\n        <view class=\"modal-header\">\n          <image class=\"warning-icon\" src=\"/static/profile/orders_warning.png\" mode=\"aspectFit\"></image>\n          <text class=\"modal-title\">操作提示</text>\n        </view>\n\n        <!-- 提示内容 -->\n        <view class=\"modal-body\">\n          <text class=\"modal-message\">是否取消报名？</text>\n        </view>\n\n        <!-- 按钮组 -->\n        <view class=\"modal-footer\">\n          <view class=\"modal-btn cancel-btn\" @click=\"closeCancelModal\">\n            暂不取消\n          </view>\n          <view class=\"modal-btn confirm-btn\" @click=\"confirmCancelRegistration\">\n            确认取消\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport {computed, ref} from 'vue';\nimport {onLoad, onPullDownRefresh, onShow} from '@dcloudio/uni-app';\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\nimport {getMyRegistrationsApi, cancelRegistrationApi} from '@/pages_sub/pages_profile/api/data/registration.js';\nimport {getEventDetailApi} from '@/api/data/event.js';\nimport {getFullImageUrl} from '@/utils/image.js';\n\n// ==================== 响应式数据定义 ====================\nconst loading = ref(false);\nconst isRefreshing = ref(false);\nconst registrationList = ref([]);\nconst loginCheckTrigger = ref(0); \nconst showCancelModal = ref(false);\nconst currentCancelItem = ref(null);\n\n// ==================== 计算属性 ====================\nconst loadMoreStatus = computed(() => {\n  if (loading.value) return 'loading';\n  return 'nomore'; // 报名记录通常不需要分页\n});\n\n// 从本地存储获取用户token\nconst getUserToken = () => {\n  try {\n    return uni.getStorageSync('token') || null;\n  } catch (e) {\n    return null;\n  }\n};\n\n// 检查用户是否已登录\nconst isLoggedIn = computed(() => {\n  // 通过 loginCheckTrigger 强制重新计算\n  loginCheckTrigger.value;\n  return !!getUserToken();\n});\n\n// ==================== 核心方法 ====================\n/**\n * 检查登录状态并刷新数据\n */\nconst checkLoginAndRefresh = async () => {\n  console.log('=== 检查登录状态并刷新数据 ===');\n  \n  // 强制触发登录状态重新计算\n  loginCheckTrigger.value++;\n  \n  const currentToken = getUserToken();\n  console.log('当前登录状态:', !!currentToken);\n  \n  if (!currentToken) {\n    console.log('用户未登录，清空数据并跳转到登录页');\n    registrationList.value = [];\n    \n    uni.showModal({\n      title: '提示',\n      content: '请先登录后查看报名记录',\n      confirmText: '去登录',\n      cancelText: '取消',\n      success: (res) => {\n        if (res.confirm) {\n          // 记录登录前的页面，便于登录成功后正确返回\n          uni.setStorageSync('loginBackPage', '/pages/profile/orders');\n          uni.navigateTo({ url: '/pages/login/index' });\n        } else {\n          // 用户取消，尝试返回上一页，如果失败则跳转到首页\n          uni.navigateBack({\n            fail: () => {\n              uni.switchTab({ url: '/pages/index/index' });\n            }\n          });\n        }\n      }\n    });\n    return;\n  }\n  \n  // 用户已登录，刷新数据\n  await loadMyRegistrations();\n};\n\n/**\n * 获取我的报名订单 \n */\nconst loadMyRegistrations = async () => {\n  if (loading.value) return;\n  \n  // 首先检查登录状态\n  if (!isLoggedIn.value) {\n    console.log('用户未登录，跳过数据加载');\n    registrationList.value = [];\n    return;\n  }\n  \n  loading.value = true;\n  \n  try {\n    // 直接调用现有接口\n    const response = await getMyRegistrationsApi();\n    \n    if (response.code === 200) {\n      // 直接使用返回数据，按时间倒序\n      const rawData = response.data || [];\n      console.log('获取报名记录成功:', rawData.length, '条');\n      \n      // 如果有数据，尝试获取活动标题信息\n      if (rawData.length > 0) {\n        registrationList.value = await enrichRegistrationData(rawData);\n      } else {\n        registrationList.value = [];\n      }\n    } else {\n      console.log('暂无报名记录');\n      registrationList.value = [];\n    }\n    \n  } catch (error) {\n    console.error('获取报名订单失败:', error);\n    \n    // 如果是401错误（未登录），特殊处理\n    if (error.message && error.message.includes('未登录')) {\n      console.log('检测到登录状态异常，清除token并提示重新登录');\n      uni.removeStorageSync('token');\n      loginCheckTrigger.value++; // 触发登录状态重新计算\n      \n      uni.showModal({\n        title: '登录过期',\n        content: '登录状态已过期，请重新登录',\n        confirmText: '重新登录',\n        showCancel: false,\n        success: (res) => {\n          if (res.confirm) {\n            // 记录登录前的页面，便于登录成功后正确返回\n            uni.setStorageSync('loginBackPage', '/pages/profile/orders');\n            uni.navigateTo({ url: '/pages/login/index' });\n          }\n        }\n      });\n      return;\n    }\n    \n    handleError(error, '获取报名记录');\n  } finally {\n    loading.value = false;\n    isRefreshing.value = false;\n  }\n};\n\n/**\n * 简化数据增强：只获取必要的活动信息\n */\nconst enrichRegistrationData = async (registrations) => {\n  // 先按报名时间倒序排列\n  const sortedRegistrations = registrations.sort((a, b) => {\n    const db = parseSafeDate(b.registrationTime)?.getTime() ?? 0;\n    const da = parseSafeDate(a.registrationTime)?.getTime() ?? 0;\n    return db - da;\n  });\n  \n  // 异步获取每个活动的基本信息\n  const enrichedData = await Promise.allSettled(\n    sortedRegistrations.map(async (registration) => {\n      try {\n        // 调用活动详情接口获取标题和封面\n        const eventDetailResponse = await getEventDetailApi(registration.eventId);\n        \n        if (eventDetailResponse.code === 200 && eventDetailResponse.data) {\n          const eventInfo = eventDetailResponse.data;\n          return {\n            ...registration,\n            title: eventInfo.title || '活动标题',\n            coverImageUrl: getFullImageUrl(eventInfo.coverImageUrl) || getFullImageUrl('/static/placeholder.png'),\n            location: eventInfo.location || '待定',\n            startTime: eventInfo.startTime\n          };\n        } else {\n          // 如果获取失败，使用默认值\n          return {\n            ...registration,\n            title: '活动标题',\n            coverImageUrl: getFullImageUrl('/static/placeholder.png'),\n            location: '待定'\n          };\n        }\n      } catch (error) {\n        console.warn('获取活动详情失败:', registration.eventId, error);\n        // 出错时使用默认值\n        return {\n          ...registration,\n          title: '活动标题',\n          coverImageUrl: getFullImageUrl('/static/placeholder.png'),\n          location: '待定'\n        };\n      }\n    })\n  );\n  \n  // 过滤成功的结果\n  return enrichedData\n    .filter(result => result.status === 'fulfilled')\n    .map(result => result.value);\n};\n\n\n\n/**\n * 跳转到活动详情页\n */\nconst goToEventDetail = (eventId) => {\n  uni.navigateTo({\n    url: `/pages_sub/pages_event/detail?id=${eventId}`\n  });\n};\n\n/**\n * 显示取消报名确认弹窗\n */\nconst showCancelConfirm = (item) => {\n  currentCancelItem.value = item;\n  showCancelModal.value = true;\n};\n\n/**\n * 关闭自定义取消弹窗\n */\nconst closeCancelModal = () => {\n  showCancelModal.value = false;\n  currentCancelItem.value = null;\n};\n\n/**\n * 确认取消报名\n */\nconst confirmCancelRegistration = () => {\n  if (currentCancelItem.value) {\n    cancelRegistration(currentCancelItem.value);\n    closeCancelModal();\n  }\n};\n\n/**\n * 取消报名\n */\nconst cancelRegistration = async (item) => {\n  try {\n    // 显示加载提示\n    uni.showLoading({\n      title: '取消中...',\n      mask: true\n    });\n\n    // 调用取消报名API\n    const response = await cancelRegistrationApi({\n      eventId: item.eventId\n    });\n\n    uni.hideLoading();\n\n    if (response.code === 200) {\n      // 取消成功，更新本地数据\n      const index = registrationList.value.findIndex(reg => \n        reg.eventId === item.eventId && reg.userId === item.userId\n      );\n      \n      if (index !== -1) {\n        // 更新状态为已取消\n        registrationList.value[index].status = 1;\n      }\n\n      uni.showToast({\n        title: '取消报名成功',\n        icon: 'success',\n        duration: 2000\n      });\n    } else {\n      throw new Error(response.msg || '取消报名失败');\n    }\n  } catch (error) {\n    uni.hideLoading();\n    console.error('取消报名失败:', error);\n    \n    uni.showToast({\n      title: error.message || '取消报名失败，请稍后重试',\n      icon: 'none',\n      duration: 3000\n    });\n  }\n};\n\n/**\n * 下拉刷新\n */\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  checkLoginAndRefresh();\n};\n\n/**\n * 跳转到登录页\n */\nconst goToLogin = () => {\n  uni.navigateTo({ url: '/pages_sub/pages_other/login' });\n};\n\n/**\n * 图片加载成功事件\n */\nconst onImageLoad = (e) => {\n  console.log('图片加载成功');\n};\n\n/**\n * 图片加载失败事件\n */\nconst onImageError = (e) => {\n  console.error('图片加载失败:', e);\n};\n\n/**\n * 上拉加载更多（暂时不需要）\n */\nconst onLoadMore = () => {\n  // 报名记录通常不需要分页\n};\n\n/**\n * 兼容 iOS 的安全日期解析：\n * - 优先将 \"yyyy-MM-dd HH:mm:ss\" 转为 \"yyyy-MM-ddTHH:mm:ss\"\n * - 若仍失败，回退为 \"yyyy/MM/dd HH:mm:ss\" 形式\n * - 同时兼容时间戳与 Date 输入\n */\nconst parseSafeDate = (input) => {\n  if (!input) return null;\n  if (input instanceof Date) {\n    return isNaN(input.getTime()) ? null : input;\n  }\n  if (typeof input === 'number') {\n    const d = new Date(input);\n    return isNaN(d.getTime()) ? null : d;\n  }\n  if (typeof input === 'string') {\n    let s = input.trim();\n    // 将 \"yyyy-MM-dd HH:mm:ss\" 转为 \"yyyy-MM-ddTHH:mm:ss\"\n    if (/^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$/.test(s)) {\n      s = s.replace(' ', 'T');\n    }\n    let d = new Date(s);\n    if (isNaN(d.getTime())) {\n      // 回退：替换日期分隔符为斜杠，适配 iOS\n      const m = s.match(/^(\\d{4})-(\\d{1,2})-(\\d{1,2})(?:[ T](\\d{1,2}):(\\d{2})(?::(\\d{2}))?)?/);\n      if (m) {\n        const y = m[1];\n        const mo = m[2];\n        const day = m[3];\n        const rest = m[4] ? ` ${m[4]}:${m[5]}:${m[6] || '00'}` : '';\n        d = new Date(`${y}/${mo}/${day}${rest}`);\n      }\n    }\n    return isNaN(d.getTime()) ? null : d;\n  }\n  return null;\n};\n\n/**\n * 格式化报名时间\n */\nconst formatRegistrationTime = (time) => {\n  if (!time) return '';\n  const date = parseSafeDate(time);\n  if (!date) return '';\n  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n};\n\n/**\n * 格式化报名状态\n */\nconst formatRegistrationStatus = (status) => {\n  return status === 0 ? '已报名' : '已取消';\n};\n\n/**\n * 获取报名状态样式类\n */\nconst getRegistrationStatusClass = (status) => {\n  return status === 0 ? 'registered' : 'cancelled';\n};\n\n/**\n * 统一错误处理\n */\nconst handleError = (error, context = '') => {\n  console.error(`${context} 错误:`, error);\n  \n  let message = '操作失败，请稍后重试';\n  \n  if (error.code) {\n    switch (error.code) {\n      case 401:\n        message = '登录已过期，请重新登录';\n        uni.reLaunch({ url: '/pages/login/index' });\n        return;\n      case 403:\n        message = '权限不足';\n        break;\n      case 404:\n        message = '请求的资源不存在';\n        break;\n      case 500:\n        message = '服务器错误，请稍后重试';\n        break;\n      default:\n        message = error.message || message;\n    }\n  }\n  \n  uni.showToast({\n    title: message,\n    icon: 'none',\n    duration: 3000\n  });\n};\n\n// ==================== 生命周期 ====================\nonLoad(() => {\n  console.log('我的报名订单页面加载');\n  checkLoginAndRefresh();\n});\n\nonShow(async () => {\n  // console.log('=== 我的报名订单页面 onShow 触发 ===');\n  \n  // 检查是否从登录页返回\n  try {\n    const loginBackPage = uni.getStorageSync('loginBackPage');\n    if (loginBackPage === '/pages/profile/orders') {\n      // console.log('检测到从登录页返回，清除标记并刷新数据');\n      uni.removeStorageSync('loginBackPage');\n      \n      // 强制触发登录状态重新计算\n      loginCheckTrigger.value++;\n      \n      // 直接加载数据，不需要再次检查登录状态\n      await loadMyRegistrations();\n      return;\n    }\n  } catch (e) {\n    console.warn('检查登录返回标记失败:', e);\n  }\n  \n  // 正常的登录状态检查\n  checkLoginAndRefresh();\n});\n\nonPullDownRefresh(async () => {\n  await onRefresh();\n  setTimeout(() => {\n    uni.stopPullDownRefresh();\n  }, 1000);\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.orders-page {\n  width: 750rpx;\n    /* 如果希望高度是固定的，请使用 height。如果希望高度能自适应内容，请使用 min-height */\n    height: 1624rpx; \n    background: #FFFFFF;\n    border-radius: 0rpx; /* border-radius: 0rpx 0rpx 0rpx 0rpx; 的简写 */\n    \n    /* 以下为原有的flex布局，建议保留以维持页面结构 */\n    display: flex;\n    flex-direction: column;\n}\n\n.orders-list-scroll {\n  flex: 1;\n   box-sizing: border-box;\n   padding: 0; /* 移除所有内边距，特别是左右的 */\n   padding-bottom: 180rpx; // 仅保留底部的，为导航栏留出空间\n}\n\n.loading-state {\n  padding: 100rpx 0;\n}\n\n.empty-state {\n  padding: 100rpx 0;\n  text-align: center;\n}\n\n.orders-list {\n  .order-card {\n    position: relative; \n    display: flex;\n    padding: 24rpx; \n    margin: 0 0 2rpx 0;\n    background-color: #ffffff;\n    border-radius: 0; \n    box-shadow: none;\n    border-bottom: 1rpx solid #F0F0F0; \n    & + .order-card {\n      margin-top: 24rpx;\n    }\n  }\n}\n\n\n\n.card-left {\n position: relative;\n   width: 336rpx;\n   height: 192rpx;\n   flex-shrink: 0;\n \n   .event-image {\n     width: 100%;\n     height: 100%;\n     display: block;\n     border-radius: 19rpx; \n   }\n\n}\n\n.card-right {\n  flex: 1;\n  padding: 20rpx 24rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  \n  .event-title {\n    width: 346rpx;\n      height: 80rpx;\n      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n      font-weight: normal;\n      font-size: 28rpx;\n      color: #23232A;\n      text-align: justified;\n      font-style: normal;\n      text-transform: none;\n      line-height: 40rpx; \n      display: -webkit-box;\n      -webkit-box-orient: vertical;\n      -webkit-line-clamp: 2;\n      overflow: hidden;\n      text-overflow: ellipsis;\n  }\n  \n  .registration-time {\n\tfont-family: 'Alibaba PuHuiTi 3.0', sans-serif;\n\tfont-weight: normal; \n\tfont-size: 22rpx;   \n\tcolor: #9B9A9A;\n\tline-height: normal;\n\tmargin-top: 8rpx;\n\n\t.time-value {\n\t\tfont-weight: inherit;\n\t\tcolor: inherit;\n\t}\n  }\n}\n\n// 新增：底部行样式\n.card-bottom-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 20rpx; \n}\n\n// 带背景图的状态样式 - 改为蓝色背景\n.status-with-bg {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 90rpx;\n  height: 36rpx;\n  background: #023F98;\n\n  .status-bg-image {\n    display: none;\n  }\n\n  .status-text {\n    color: #ffffff;\n    font-size: 24rpx;\n    font-weight: 500;\n    white-space: nowrap;\n  }\n}\n\n// 已取消状态样式\n.status-cancelled {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 90rpx;\n  height: 36rpx;\n  background: #CBCBCB;\n  // border-radius: 8rpx;\n\n  .status-text {\n    color: #ffffff;\n    font-size: 24rpx;\n    font-weight: 500;\n    white-space: nowrap;\n  }\n}\n\n.registration-status-tag {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  color: #ffffff;\n  font-size: 24rpx; \n  padding: 4rpx 12rpx;\n  border-radius: 8rpx; \n  font-weight: 500;\n  height: 36rpx;\n  box-sizing: border-box;\n\n  &.registered {\n    background-color: #409eff; \n  }\n\n  &.cancelled {\n    background-color: #909399;\n  }\n}\n\n.card-actions {\n  display: flex;\n  align-items: center;\n}\n\n.loadmore-wrapper {\n  padding: 20rpx 0 40rpx 0;\n}\n\n/* 绝对定位的取消报名按钮样式 */\n.cancel-btn-absolute {\n  position: absolute;\n  right: 24rpx; \n  bottom: 44rpx;\n\n  /* 布局和尺寸 */\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  width: 96rpx;\n  height: 36rpx;\n  box-sizing: border-box;\n\n  /* 字体和外观 */\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n  font-weight: normal;\n  font-size: 24rpx;\n  color: #66666E;\n  text-align: left;\n  font-style: normal;\n  text-transform: none;\n  letter-spacing: 0;\n\n  /* 其他样式 */\n  background: none;\n  border: none;\n  border-radius: 8rpx;\n\n  /* 点击效果 */\n  &:active {\n    opacity: 0.7;\n  }\n}\n\n/* 自定义取消报名弹窗样式 */\n.cancel-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.cancel-modal-content {\n  width: 654rpx;\n  height: 420rpx;\n  background: #FFFFFF;\n  border-radius: 16rpx;\n  position: relative;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n}\n\n.modal-header {\n  position: absolute;\n  top: 42rpx;\n  left: 48rpx;\n  display: flex;\n  align-items: center;\n\n  .warning-icon {\n    width: 48rpx;\n    height: 40rpx;\n    margin-right: 16rpx;\n  }\n\n  .modal-title {\n    width: 142rpx;\n    height: 44rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n    font-weight: normal;\n    font-size: 36rpx;\n    color: #23232A;\n    line-height: 44rpx;\n    text-align: center;\n    font-style: normal;\n    text-transform: none;\n  }\n}\n\n.modal-body {\n  position: absolute;\n  top: 176rpx;\n  left: 50%;\n  transform: translateX(-50%);\n\n  .modal-message {\n    font-family: 'Alibaba PuHuiTi 3.0', sans-serif;\n    font-size: 32rpx;\n    color: #23232A;\n    line-height: 44rpx;\n    text-align: center;\n\tfont-weight: normal;\n    text-transform: none;\n    display: block;\n  }\n}\n\n.modal-footer {\n  position: absolute;\n  top: 316rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  gap: 24rpx;\n\n  .modal-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    transition: opacity 0.2s;\n\n    &:active {\n      opacity: 0.8;\n    }\n  }\n\n  .cancel-btn {\n      width: 292rpx;\n      height: 76rpx;\n      background: rgba(42, 97, 241, 0.1); \n      border-radius: 8rpx;\n      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n      font-weight: normal; \n      font-size: 28rpx;\n      color: #23232A;\n      line-height: 44rpx;\n  }\n\n  .confirm-btn {\n    width: 292rpx;\n    height: 76rpx;\n    background: #023F98;\n    border-radius: 8rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', sans-serif;\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #ffffff;\n  }\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 750rpx) {\n  .order-card {\n    .card-left {\n      width: 200rpx;\n      height: 160rpx;\n    }\n\n    .card-right {\n      .event-title {\n        font-size: 28rpx;\n      }\n    }\n  }\n}\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_profile/orders.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "getMyRegistrationsApi", "getEventDetailApi", "getFullImageUrl", "cancelRegistrationApi", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAoKA,MAAM,eAAe,MAAW;;;;AAMhC,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,mBAAmBA,cAAAA,IAAI,CAAA,CAAE;AAC/B,UAAM,oBAAoBA,cAAAA,IAAI,CAAC;AAC/B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,oBAAoBA,cAAAA,IAAI,IAAI;AAGlC,UAAM,iBAAiBC,cAAQ,SAAC,MAAM;AACpC,UAAI,QAAQ;AAAO,eAAO;AAC1B,aAAO;AAAA,IACT,CAAC;AAGD,UAAM,eAAe,MAAM;AACzB,UAAI;AACF,eAAOC,oBAAI,eAAe,OAAO,KAAK;AAAA,MACvC,SAAQ,GAAG;AACV,eAAO;AAAA,MACR;AAAA,IACH;AAGA,UAAM,aAAaD,cAAQ,SAAC,MAAM;AAEhC,wBAAkB;AAClB,aAAO,CAAC,CAAC;IACX,CAAC;AAMD,UAAM,uBAAuB,YAAY;AACvCC,oBAAAA,MAAY,MAAA,OAAA,6CAAA,qBAAqB;AAGjC,wBAAkB;AAElB,YAAM,eAAe;AACrBA,oBAAA,MAAA,MAAA,OAAA,6CAAY,WAAW,CAAC,CAAC,YAAY;AAErC,UAAI,CAAC,cAAc;AACjBA,sBAAAA,MAAY,MAAA,OAAA,6CAAA,mBAAmB;AAC/B,yBAAiB,QAAQ;AAEzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AAEfA,4BAAAA,MAAI,eAAe,iBAAiB,uBAAuB;AAC3DA,4BAAAA,MAAI,WAAW,EAAE,KAAK,qBAAsB,CAAA;AAAA,YACtD,OAAe;AAELA,4BAAAA,MAAI,aAAa;AAAA,gBACf,MAAM,MAAM;AACVA,gCAAAA,MAAI,UAAU,EAAE,KAAK,qBAAsB,CAAA;AAAA,gBAC5C;AAAA,cACb,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACP,CAAK;AACD;AAAA,MACD;AAGD,YAAM,oBAAmB;AAAA,IAC3B;AAKA,UAAM,sBAAsB,YAAY;AACtC,UAAI,QAAQ;AAAO;AAGnB,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,gEAAY,cAAc;AAC1B,yBAAiB,QAAQ;AACzB;AAAA,MACD;AAED,cAAQ,QAAQ;AAEhB,UAAI;AAEF,cAAM,WAAW,MAAMC,8CAAAA;AAEvB,YAAI,SAAS,SAAS,KAAK;AAEzB,gBAAM,UAAU,SAAS,QAAQ;AACjCD,wFAAY,aAAa,QAAQ,QAAQ,GAAG;AAG5C,cAAI,QAAQ,SAAS,GAAG;AACtB,6BAAiB,QAAQ,MAAM,uBAAuB,OAAO;AAAA,UACrE,OAAa;AACL,6BAAiB,QAAQ;UAC1B;AAAA,QACP,OAAW;AACLA,wBAAAA,MAAY,MAAA,OAAA,6CAAA,QAAQ;AACpB,2BAAiB,QAAQ;QAC1B;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,6CAAc,aAAa,KAAK;AAGhC,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAK,GAAG;AAClDA,wBAAAA,MAAY,MAAA,OAAA,6CAAA,0BAA0B;AACtCA,8BAAI,kBAAkB,OAAO;AAC7B,4BAAkB;AAElBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AAEfA,8BAAAA,MAAI,eAAe,iBAAiB,uBAAuB;AAC3DA,8BAAAA,MAAI,WAAW,EAAE,KAAK,qBAAsB,CAAA;AAAA,cAC7C;AAAA,YACF;AAAA,UACT,CAAO;AACD;AAAA,QACD;AAED,oBAAY,OAAO,QAAQ;AAAA,MAC/B,UAAY;AACR,gBAAQ,QAAQ;AAChB,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;AAKA,UAAM,yBAAyB,OAAO,kBAAkB;AAEtD,YAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG,MAAM;;AACvD,cAAM,OAAK,mBAAc,EAAE,gBAAgB,MAAhC,mBAAmC,cAAa;AAC3D,cAAM,OAAK,mBAAc,EAAE,gBAAgB,MAAhC,mBAAmC,cAAa;AAC3D,eAAO,KAAK;AAAA,MAChB,CAAG;AAGD,YAAM,eAAe,MAAM,QAAQ;AAAA,QACjC,oBAAoB,IAAI,OAAO,iBAAiB;AAC9C,cAAI;AAEF,kBAAM,sBAAsB,MAAME,eAAAA,kBAAkB,aAAa,OAAO;AAExE,gBAAI,oBAAoB,SAAS,OAAO,oBAAoB,MAAM;AAChE,oBAAM,YAAY,oBAAoB;AACtC,qBAAO;AAAA,gBACL,GAAG;AAAA,gBACH,OAAO,UAAU,SAAS;AAAA,gBAC1B,eAAeC,YAAe,gBAAC,UAAU,aAAa,KAAKA,YAAAA,gBAAgB,yBAAyB;AAAA,gBACpG,UAAU,UAAU,YAAY;AAAA,gBAChC,WAAW,UAAU;AAAA,cACjC;AAAA,YACA,OAAe;AAEL,qBAAO;AAAA,gBACL,GAAG;AAAA,gBACH,OAAO;AAAA,gBACP,eAAeA,YAAe,gBAAC,yBAAyB;AAAA,gBACxD,UAAU;AAAA,cACtB;AAAA,YACS;AAAA,UACF,SAAQ,OAAO;AACdH,gCAAa,MAAA,QAAA,6CAAA,aAAa,aAAa,SAAS,KAAK;AAErD,mBAAO;AAAA,cACL,GAAG;AAAA,cACH,OAAO;AAAA,cACP,eAAeG,YAAe,gBAAC,yBAAyB;AAAA,cACxD,UAAU;AAAA,YACpB;AAAA,UACO;AAAA,QACP,CAAK;AAAA,MACL;AAGE,aAAO,aACJ,OAAO,YAAU,OAAO,WAAW,WAAW,EAC9C,IAAI,YAAU,OAAO,KAAK;AAAA,IAC/B;AAOA,UAAM,kBAAkB,CAAC,YAAY;AACnCH,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oCAAoC,OAAO;AAAA,MACpD,CAAG;AAAA,IACH;AAKA,UAAM,oBAAoB,CAAC,SAAS;AAClC,wBAAkB,QAAQ;AAC1B,sBAAgB,QAAQ;AAAA,IAC1B;AAKA,UAAM,mBAAmB,MAAM;AAC7B,sBAAgB,QAAQ;AACxB,wBAAkB,QAAQ;AAAA,IAC5B;AAKA,UAAM,4BAA4B,MAAM;AACtC,UAAI,kBAAkB,OAAO;AAC3B,2BAAmB,kBAAkB,KAAK;AAC1C;MACD;AAAA,IACH;AAKA,UAAM,qBAAqB,OAAO,SAAS;AACzC,UAAI;AAEFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,cAAM,WAAW,MAAMI,oEAAsB;AAAA,UAC3C,SAAS,KAAK;AAAA,QACpB,CAAK;AAEDJ,sBAAG,MAAC,YAAW;AAEf,YAAI,SAAS,SAAS,KAAK;AAEzB,gBAAM,QAAQ,iBAAiB,MAAM;AAAA,YAAU,SAC7C,IAAI,YAAY,KAAK,WAAW,IAAI,WAAW,KAAK;AAAA,UAC5D;AAEM,cAAI,UAAU,IAAI;AAEhB,6BAAiB,MAAM,KAAK,EAAE,SAAS;AAAA,UACxC;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACP,OAAW;AACL,gBAAM,IAAI,MAAM,SAAS,OAAO,QAAQ;AAAA,QACzC;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,6CAAc,WAAW,KAAK;AAE9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;AAKA,UAAM,YAAY,MAAM;AACtB,mBAAa,QAAQ;AACrB;IACF;AAKA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,WAAW,EAAE,KAAK,+BAAgC,CAAA;AAAA,IACxD;AAKA,UAAM,cAAc,CAAC,MAAM;AACzBA,oBAAAA,MAAY,MAAA,OAAA,6CAAA,QAAQ;AAAA,IACtB;AAKA,UAAM,eAAe,CAAC,MAAM;AAC1BA,oBAAA,MAAA,MAAA,SAAA,6CAAc,WAAW,CAAC;AAAA,IAC5B;AAKA,UAAM,aAAa,MAAM;AAAA,IAEzB;AAQA,UAAM,gBAAgB,CAAC,UAAU;AAC/B,UAAI,CAAC;AAAO,eAAO;AACnB,UAAI,iBAAiB,MAAM;AACzB,eAAO,MAAM,MAAM,QAAS,CAAA,IAAI,OAAO;AAAA,MACxC;AACD,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,IAAI,KAAK,KAAK;AACxB,eAAO,MAAM,EAAE,QAAS,CAAA,IAAI,OAAO;AAAA,MACpC;AACD,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,IAAI,MAAM;AAEd,YAAI,wCAAwC,KAAK,CAAC,GAAG;AACnD,cAAI,EAAE,QAAQ,KAAK,GAAG;AAAA,QACvB;AACD,YAAI,IAAI,IAAI,KAAK,CAAC;AAClB,YAAI,MAAM,EAAE,QAAO,CAAE,GAAG;AAEtB,gBAAM,IAAI,EAAE,MAAM,qEAAqE;AACvF,cAAI,GAAG;AACL,kBAAM,IAAI,EAAE,CAAC;AACb,kBAAM,KAAK,EAAE,CAAC;AACd,kBAAM,MAAM,EAAE,CAAC;AACf,kBAAM,OAAO,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,KAAK;AACzD,gBAAI,oBAAI,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,GAAG,IAAI,EAAE;AAAA,UACxC;AAAA,QACF;AACD,eAAO,MAAM,EAAE,QAAS,CAAA,IAAI,OAAO;AAAA,MACpC;AACD,aAAO;AAAA,IACT;AAKA,UAAM,yBAAyB,CAAC,SAAS;AACvC,UAAI,CAAC;AAAM,eAAO;AAClB,YAAM,OAAO,cAAc,IAAI;AAC/B,UAAI,CAAC;AAAM,eAAO;AAClB,aAAO,GAAG,KAAK,YAAa,CAAA,IAAI,OAAO,KAAK,aAAa,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACnN;AAKA,UAAM,2BAA2B,CAAC,WAAW;AAC3C,aAAO,WAAW,IAAI,QAAQ;AAAA,IAChC;AAKA,UAAM,6BAA6B,CAAC,WAAW;AAC7C,aAAO,WAAW,IAAI,eAAe;AAAA,IACvC;AAKA,UAAM,cAAc,CAAC,OAAO,UAAU,OAAO;AAC3CA,0BAAc,MAAA,SAAA,6CAAA,GAAG,OAAO,QAAQ,KAAK;AAErC,UAAI,UAAU;AAEd,UAAI,MAAM,MAAM;AACd,gBAAQ,MAAM,MAAI;AAAA,UAChB,KAAK;AACH,sBAAU;AACVA,0BAAAA,MAAI,SAAS,EAAE,KAAK,qBAAsB,CAAA;AAC1C;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF,KAAK;AACH,sBAAU;AACV;AAAA,UACF;AACE,sBAAU,MAAM,WAAW;AAAA,QAC9B;AAAA,MACF;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AAAA,IACH;AAGAK,kBAAAA,OAAO,MAAM;AACXL,oBAAAA,MAAA,MAAA,OAAA,6CAAY,YAAY;AACxB;IACF,CAAC;AAEDM,kBAAAA,OAAO,YAAY;AAIjB,UAAI;AACF,cAAM,gBAAgBN,cAAAA,MAAI,eAAe,eAAe;AACxD,YAAI,kBAAkB,yBAAyB;AAE7CA,8BAAI,kBAAkB,eAAe;AAGrC,4BAAkB;AAGlB,gBAAM,oBAAmB;AACzB;AAAA,QACD;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAA,MAAA,MAAA,QAAA,6CAAa,eAAe,CAAC;AAAA,MAC9B;AAGD;IACF,CAAC;AAEDO,kBAAAA,kBAAkB,YAAY;AAC5B,YAAM,UAAS;AACf,iBAAW,MAAM;AACfP,sBAAG,MAAC,oBAAmB;AAAA,MACxB,GAAE,GAAI;AAAA,IACT,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3mBD,GAAG,WAAW,eAAe;"}