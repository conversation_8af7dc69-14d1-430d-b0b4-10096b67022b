"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_image = require("../../utils/image.js");
const _sfc_main = {
  __name: "EventCalendarTimeline",
  props: {
    groups: { type: Array, required: true },
    hasMore: { type: Boolean, default: false },
    notchLeft: { type: String, default: "60rpx" }
  },
  setup(__props) {
    const formatEventLocation = (event) => {
      if (event.city && event.city.trim()) {
        return event.city.trim().replace(/市$/, "");
      }
      return "待定";
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.notchLeft,
        b: common_vendor.f(__props.groups, (group, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(group.formattedDate),
            b: common_vendor.t(group.dayOfWeek),
            c: index < __props.groups.length - 1
          }, index < __props.groups.length - 1 ? {} : {}, {
            d: common_vendor.f(group.events, (event, k1, i1) => {
              return {
                a: common_vendor.unref(utils_image.getFullImageUrl)(event.iconUrl),
                b: common_vendor.t(event.title),
                c: common_vendor.t(formatEventLocation(event)),
                d: event.id,
                e: common_vendor.o(($event) => _ctx.$emit("click-item", event), event.id)
              };
            }),
            e: group.date
          });
        }),
        c: common_assets._imports_0$10,
        d: __props.hasMore
      }, __props.hasMore ? {
        e: common_vendor.o(($event) => _ctx.$emit("view-more"))
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ba5a4f4a"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/event/EventCalendarTimeline.js.map
