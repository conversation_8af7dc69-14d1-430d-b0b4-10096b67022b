"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_data_user = require("../../api/data/user.js");
const pages_sub_pages_other_api_data_policy = require("./api/data/policy.js");
if (!Array) {
  const _easycom_up_navbar2 = common_vendor.resolveComponent("up-navbar");
  _easycom_up_navbar2();
}
const _easycom_up_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
if (!Math) {
  _easycom_up_navbar();
}
const _sfc_main = /* @__PURE__ */ Object.assign({
  name: "LoginPage"
}, {
  __name: "login",
  setup(__props) {
    const isAgreementChecked = common_vendor.ref(false);
    const policyVersions = common_vendor.ref({
      userAgreement: null,
      privacyPolicy: null
    });
    const toggleAgreement = () => {
      isAgreementChecked.value = !isAgreementChecked.value;
    };
    const onGetPhoneNumber = async (e) => {
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:89", "=== 开始微信手机号快捷登录流程 ===");
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:90", "授权事件详情:", e);
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:91", "e.detail:", e.detail);
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:92", "e.detail.code:", e.detail.code);
      if (!isAgreementChecked.value) {
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:96", "用户未同意协议");
        common_vendor.index.showToast({
          title: "请先同意用户协议和隐私政策",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:103", "用户已同意协议");
      if (!e.detail.code) {
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:107", "微信未返回授权code");
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:108", "错误信息:", e.detail.errMsg);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:109", "可能原因：1. 用户拒绝授权 2. 配置问题 3. 网络异常");
        common_vendor.index.showToast({
          title: "获取手机号失败，请重试",
          icon: "none"
        });
        return;
      }
      const phoneCode = e.detail.code;
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:118", "获取到微信手机号授权码:", phoneCode);
      common_vendor.index.showLoading({ title: "正在登录..." });
      try {
        common_vendor.index.removeStorageSync("token");
        common_vendor.index.removeStorageSync("userInfo");
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:127", "开始执行uni.login...");
        const loginRes = await common_vendor.index.login();
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:129", "uni.login结果:", loginRes);
        const loginCode = loginRes.code;
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:131", "获得loginCode:", loginCode);
        const res = await api_data_user.wxLoginApi(loginCode);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:135", "后端登录成功:", res);
        const token = res.token;
        common_vendor.index.setStorageSync("token", token);
        common_vendor.index.setStorageSync("userInfo", res.userInfo);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:143", "使用phoneCode:", phoneCode);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:144", "使用token:", token);
        const phoneRes = await api_data_user.getPhoneNumberApi(phoneCode);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:146", "获取手机号成功:", phoneRes);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:149", "开始更新本地用户信息...");
        try {
          const userInfo = common_vendor.index.getStorageSync("userInfo");
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:152", "当前userInfo:", userInfo);
          if (!userInfo) {
            common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:155", "userInfo为空，创建新的用户信息对象");
            const newUserInfo = {
              phoneNumber: phoneRes.phoneNumber
            };
            common_vendor.index.setStorageSync("userInfo", newUserInfo);
          } else {
            common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:161", "更新现有userInfo的手机号");
            userInfo.phoneNumber = phoneRes.phoneNumber;
            common_vendor.index.setStorageSync("userInfo", userInfo);
          }
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:166", "用户信息更新完成");
        } catch (updateError) {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:168", "更新用户信息时出错:", updateError);
          throw updateError;
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:172", "登录流程完全成功，最终保存的数据:");
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:173", "- token:", common_vendor.index.getStorageSync("token"));
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:174", "- userInfo:", common_vendor.index.getStorageSync("userInfo"));
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:176", "准备隐藏加载提示...");
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:179", "准备显示成功提示...");
        common_vendor.index.showToast({ title: "登录成功", icon: "success" });
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:183", "开始上报协议同意记录...");
        try {
          await reportPolicyAcceptance();
        } catch (error) {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:187", "协议同意记录上报异常:", error);
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:191", "设置延迟返回定时器...");
        setTimeout(() => {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:193", "定时器触发，准备返回上一页...");
          try {
            const loginBackPage = common_vendor.index.getStorageSync("loginBackPage");
            if (loginBackPage) {
              common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:199", "检测到指定的返回页面:", loginBackPage);
              common_vendor.index.navigateBack({
                success: () => {
                  common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:203", "成功返回上一页");
                },
                fail: (err) => {
                  common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:206", "返回上一页失败，尝试直接跳转到指定页面:", err);
                  if (loginBackPage.startsWith("/pages/")) {
                    common_vendor.index.redirectTo({
                      url: loginBackPage,
                      fail: () => {
                        common_vendor.index.switchTab({ url: "/pages/index/index" });
                      }
                    });
                  }
                }
              });
            } else {
              common_vendor.index.navigateBack({
                success: () => {
                  common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:223", "成功返回上一页");
                },
                fail: (err) => {
                  common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:226", "返回上一页失败，跳转到首页:", err);
                  common_vendor.index.switchTab({ url: "/pages/index/index" });
                }
              });
            }
          } catch (e2) {
            common_vendor.index.__f__("warn", "at pages_sub/pages_other/login.vue:233", "检查返回页面标记失败:", e2);
            common_vendor.index.navigateBack({
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:237", "返回上一页失败，跳转到首页:", err);
                common_vendor.index.switchTab({ url: "/pages/index/index" });
              }
            });
          }
        }, 2e3);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:244", "登录方法执行完成，等待定时器触发返回...");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:247", "登录过程中发生错误:", error);
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:248", "错误详情:", {
          message: error.message,
          stack: error.stack,
          name: error.name
        });
        common_vendor.index.hideLoading();
        const errorMessage = error.message || "网络请求失败";
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:257", "显示错误提示:", errorMessage);
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
      }
    };
    const goToUserAgreement = () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:269", "点击用户协议链接");
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_other/policy?type=user_agreement",
        success: () => {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:273", "成功跳转到用户协议页面");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:276", "跳转用户协议页面失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const goToPrivacyPolicy = () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:288", "点击隐私政策链接");
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_other/policy?type=privacy_policy",
        success: () => {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:292", "成功跳转到隐私政策页面");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:295", "跳转隐私政策页面失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const loadPolicyVersions = async () => {
      try {
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:312", "开始加载协议版本信息...");
        const [userAgreementRes, privacyPolicyRes] = await Promise.all([
          pages_sub_pages_other_api_data_policy.getLatestPolicyApi("user_agreement"),
          pages_sub_pages_other_api_data_policy.getLatestPolicyApi("privacy_policy")
        ]);
        if (userAgreementRes && userAgreementRes.code === 200 && userAgreementRes.data) {
          policyVersions.value.userAgreement = userAgreementRes.data.version;
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:322", "用户协议版本:", userAgreementRes.data.version);
        }
        if (privacyPolicyRes && privacyPolicyRes.code === 200 && privacyPolicyRes.data) {
          policyVersions.value.privacyPolicy = privacyPolicyRes.data.version;
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:327", "隐私政策版本:", privacyPolicyRes.data.version);
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:330", "协议版本信息加载完成:", policyVersions.value);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:332", "加载协议版本信息失败:", error);
        policyVersions.value.userAgreement = "1.0.0";
        policyVersions.value.privacyPolicy = "1.0.0";
      }
    };
    const reportPolicyAcceptance = async () => {
      try {
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:342", "=== 开始上报协议同意记录 ===");
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:343", "当前协议版本信息:", policyVersions.value);
        if (!policyVersions.value.userAgreement || !policyVersions.value.privacyPolicy) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:347", "协议版本信息不完整，尝试重新加载...");
          await loadPolicyVersions();
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:349", "重新加载后的协议版本信息:", policyVersions.value);
        }
        const reports = [];
        if (policyVersions.value.userAgreement) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:356", "准备上报用户协议，版本:", policyVersions.value.userAgreement);
          reports.push(
            pages_sub_pages_other_api_data_policy.acceptPolicyApi("user_agreement", policyVersions.value.userAgreement)
          );
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_other/login.vue:361", "用户协议版本为空，使用默认版本1.0.0");
          reports.push(
            pages_sub_pages_other_api_data_policy.acceptPolicyApi("user_agreement", "1.0.0")
          );
        }
        if (policyVersions.value.privacyPolicy) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:369", "准备上报隐私政策，版本:", policyVersions.value.privacyPolicy);
          reports.push(
            pages_sub_pages_other_api_data_policy.acceptPolicyApi("privacy_policy", policyVersions.value.privacyPolicy)
          );
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_other/login.vue:374", "隐私政策版本为空，使用默认版本1.0.0");
          reports.push(
            pages_sub_pages_other_api_data_policy.acceptPolicyApi("privacy_policy", "1.0.0")
          );
        }
        if (reports.length === 0) {
          common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:381", "无法创建上报请求");
          return;
        }
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:385", `准备并行执行${reports.length}个上报请求...`);
        const results = await Promise.all(reports);
        common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:389", "协议同意记录上报原始结果:", results);
        let successCount = 0;
        results.forEach((result, index) => {
          const type = index === 0 ? "用户协议" : "隐私政策";
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:395", `${type}上报结果:`, result);
          if (result && result.code === 200) {
            successCount++;
            common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:399", `${type}同意记录上报成功`);
          } else {
            common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:401", `${type}同意记录上报失败:`, result);
          }
        });
        if (successCount === results.length) {
          common_vendor.index.__f__("log", "at pages_sub/pages_other/login.vue:406", "所有协议同意记录上报成功");
        } else {
          common_vendor.index.__f__("warn", "at pages_sub/pages_other/login.vue:408", `部分协议同意记录上报失败，成功数量: ${successCount}/${results.length}`);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:412", "上报协议同意记录过程中发生异常:", error);
        common_vendor.index.__f__("error", "at pages_sub/pages_other/login.vue:413", "错误详情:", {
          message: error.message,
          stack: error.stack
        });
      }
    };
    common_vendor.onLoad(() => {
      loadPolicyVersions();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$6,
        b: common_vendor.p({
          title: "登录",
          autoBack: true,
          safeAreaInsetTop: true,
          fixed: true,
          placeholder: true,
          bgColor: "transparent",
          zIndex: 99,
          leftIconColor: "#333333",
          titleStyle: {
            fontFamily: "Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif",
            fontWeight: "normal",
            fontSize: "32rpx",
            color: "#000000",
            lineHeight: "44rpx"
          }
        }),
        c: common_assets._imports_1$3,
        d: common_vendor.o(onGetPhoneNumber),
        e: isAgreementChecked.value
      }, isAgreementChecked.value ? {} : {}, {
        f: isAgreementChecked.value ? 1 : "",
        g: common_vendor.o(goToUserAgreement),
        h: common_vendor.o(goToPrivacyPolicy),
        i: common_vendor.o(toggleAgreement)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-de5c63ce"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_other/login.js.map
