package com.hongda.framework.aspectj;

import com.hongda.common.annotation.OssUrl;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.page.TableDataInfo;
import com.hongda.common.core.service.OssFileStorageService;
import com.hongda.common.utils.StringUtils;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Collection;

@Aspect
@Component
public class OssUrlAspect {

    @Autowired
    private OssFileStorageService ossFileStorageService;

    // 定义切点，拦截所有Controller层方法的返回值
    @Pointcut("execution(public * com.hongda..*Controller.*(..))")
    public void controllerResultPointcut() {
    }

    // 在方法成功返回后执行
    @AfterReturning(returning = "result", pointcut = "controllerResultPointcut()")
    public void afterReturning(Object result) throws Throwable {
        if (result instanceof AjaxResult) {
            Object data = ((AjaxResult) result).get("data");
            processObject(data);
        } else if (result instanceof TableDataInfo) {
            Object data = ((TableDataInfo) result).getRows();
            processObject(data);
        }
    }

    /**
     * 递归处理对象，将带有 @OssUrl 注解的字段转换为签名URL
     * @param obj 待处理的对象 (可以是单个对象、集合、Map等)
     */
    private void processObject(Object obj) throws IllegalAccessException {
        if (obj == null) {
            return;
        }

        // 如果是集合类型，遍历集合中的每个元素
        if (obj instanceof Collection) {
            for (Object item : (Collection<?>) obj) {
                processObject(item);
            }
            return; // 处理完集合后直接返回
        }

        // 获取当前对象的所有字段
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            // 检查字段上是否有 @OssUrl 注解
            if (field.isAnnotationPresent(OssUrl.class)) {
                field.setAccessible(true); // 允许访问私有字段
                Object value = field.get(obj); // 获取字段的当前值 (即 objectName)

                if (value instanceof String && StringUtils.isNotEmpty((String) value)) {
                    // 调用服务，生成签名URL
                    String signedUrl = ossFileStorageService.getSignedUrl((String) value, 15);
                    // 将字段的值更新为生成的签名URL
                    field.set(obj, signedUrl);
                }
            }
        }
    }
}