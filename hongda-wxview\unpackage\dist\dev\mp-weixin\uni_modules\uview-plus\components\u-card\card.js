"use strict";
const Card = {
  // card组件的props
  card: {
    full: false,
    title: "",
    titleColor: "#303133",
    titleSize: "15px",
    subTitle: "",
    subTitleColor: "#909399",
    subTitleSize: "13px",
    border: true,
    index: "",
    margin: "15px",
    borderRadius: "8px",
    headStyle: {},
    bodyStyle: {},
    footStyle: {},
    headBorderBottom: true,
    footBorderTop: true,
    thumb: "",
    thumbWidth: "30px",
    thumbCircle: false,
    padding: "15px",
    paddingHead: "",
    paddingBody: "",
    paddingFoot: "",
    showHead: true,
    showFoot: true,
    boxShadow: "none"
  }
};
exports.Card = Card;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uview-plus/components/u-card/card.js.map
