package com.hongda.common.core.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.hongda.common.config.AliOssConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;
import java.util.Date;

@Slf4j
@Service
public class OssFileStorageService {

    @Autowired
    private OSS ossClient;

    @Autowired
    private AliOssConfigProperties aliOssConfigProperties;

    /**
     * 上传文件
     * @param objectName  文件名
     * @param inputStream 文件输入流
     * @return 文件的公开访问URL
     */
    public String upload(String objectName, InputStream inputStream) {
        String bucketName = aliOssConfigProperties.getBucketName();
        String endpoint = aliOssConfigProperties.getEndpoint();

        log.info("OSS文件上传开始: {}, bucket: {}", objectName, bucketName);

        try {
            // 上传文件。
            ossClient.putObject(bucketName, objectName, inputStream);
        } catch (Exception e) {
            log.error("OSS文件上传失败", e);
            // 抛出异常或返回null，取决于您的错误处理策略
            throw new RuntimeException("文件上传到OSS失败", e);
        }

        // 构建文件的公开访问URL
        String url = "https://" + bucketName + "." + endpoint + "/" + objectName;
        log.info("OSS文件上传成功, URL: {}", url);
        return url;
    }

    /**
     * 【重要新增方法】为私有文件生成带签名的临时访问URL
     *
     * @param objectName 文件的对象键（文件名）
     * @param expireInMinutes URL的有效时间（分钟）
     * @return 带签名的URL字符串
     */
    public String getSignedUrl(String objectName, int expireInMinutes) {
        if (objectName == null) {
            return null;
        }

        // 设置URL过期时间
        Date expiration = new Date(new Date().getTime() + expireInMinutes * 60 * 1000L);

        // 创建请求
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(aliOssConfigProperties.getBucketName(), objectName);
        request.setExpiration(expiration);

        // 生成签名URL
        URL signedUrl = ossClient.generatePresignedUrl(request);
        log.info("为文件 {} 生成签名URL成功", objectName);
        return signedUrl.toString();
    }
}