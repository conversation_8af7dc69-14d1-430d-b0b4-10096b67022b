"use strict";
require("../../../../common/vendor.js");
const utils_request = require("../../../../utils/request.js");
const getFormDefinitionApi = (eventId) => {
  return utils_request.http.get(`/content/form-definition/event/${eventId}`);
};
const checkRegistrationStatusApi = (eventId) => {
  return utils_request.http.get("/registration/check", { eventId });
};
const submitRegistrationApi = (data) => {
  const requestData = {
    eventId: data.eventId,
    formData: JSON.stringify(data.formData)
    // 将表单数据转换为JSON字符串
  };
  return utils_request.http.post("/registration/register", requestData);
};
exports.checkRegistrationStatusApi = checkRegistrationStatusApi;
exports.getFormDefinitionApi = getFormDefinitionApi;
exports.submitRegistrationApi = submitRegistrationApi;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages_sub/pages_event/api/data/registration.js.map
