{"version": 3, "file": "config.js", "sources": ["utils/config.js"], "sourcesContent": ["/**\r\n * 项目配置文件\r\n */\r\n\r\n// 开发环境配置\r\nconst development = {\r\n  baseUrl: 'http://localhost:80', // 开发环境API基础URL\r\n  imageBaseUrl: 'http://localhost:80', // 图片服务器基础URL\r\n  timeout: 10000, // 请求超时时间\r\n  debug: true // 是否开启调试模式\r\n};\r\n\r\n// 生产环境配置\r\nconst production = {\r\n  baseUrl: 'https://api.hongda.com', // 生产环境API基础URL\r\n  imageBaseUrl: 'https://img.hongda.com', // 图片服务器基础URL\r\n  timeout: 10000, // 请求超时时间\r\n  debug: false // 是否开启调试模式\r\n};\r\n\r\n// 根据环境变量选择配置\r\nconst config = process.env.NODE_ENV === 'production' ? production : development;\r\n\r\n// API路径配置 - 小程序专用接口（不包含/api/v1前缀，因为request.js已经添加了）\r\nexport const API_PATHS = {\r\n  EVENT_LIST: '/events/list',\r\n  EVENT_DETAIL: '/events',\r\n  EVENT_EXPORT: '/content/pages_event/export',\r\n  EVENT_LOCATIONS: '/events/locations',\r\n  EVENT_HOT: '/events/hot',\r\n  EVENT_UPCOMING: '/events/upcoming',\r\n  EVENT_SEARCH: '/events/search',\r\n  EVENT_BY_LOCATION: '/events/by-location',\r\n  EVENT_LOCATION_STATS: '/events/location-stats'\r\n};\r\n\r\n// 分页配置\r\nexport const PAGE_CONFIG = {\r\n  DEFAULT_PAGE_SIZE: 10,\r\n  MAX_PAGE_SIZE: 50\r\n};\r\n\r\n// 导出基础URL便于其他模块使用\r\nexport const BASE_URL = config.baseUrl;\r\nexport const IMAGE_BASE_URL = config.imageBaseUrl;\r\n\r\nexport default config;"], "names": [], "mappings": ";AAKA,MAAM,cAAc;AAAA,EAClB,SAAS;AAAA;AAAA,EACT,cAAc;AAAA;AAAA,EACd,SAAS;AAAA;AAAA,EACT,OAAO;AAAA;AACT;AAWM,MAAA,SAA8D;AAG7D,MAAM,YAAY;AAAA,EACvB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,sBAAsB;AACxB;AAGO,MAAM,cAAc;AAAA,EACzB,mBAAmB;AAAA,EACnB,eAAe;AACjB;AAGO,MAAM,WAAW,OAAO;AACxB,MAAM,iBAAiB,OAAO;;;;;;"}