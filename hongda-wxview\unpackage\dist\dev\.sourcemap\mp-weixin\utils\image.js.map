{"version": 3, "file": "image.js", "sources": ["utils/image.js"], "sourcesContent": ["import config from '@/utils/config.js';\r\n\r\nexport function getFullImageUrl(relativePath) {\r\n\tif (!relativePath) {\r\n\t\t// 为活动图标提供默认值，使用现有的蓝色logo作为默认图标\r\n\t\treturn '/static/pages_event/美妆logo蓝@2x.png';\r\n\t}\r\n\t// 如果是完整的URL，直接返回。不要强制修改协议！\r\n\tif (relativePath.startsWith('http')) {\r\n\t\treturn relativePath;\r\n\t}\r\n\t// 如果是相对路径，则拼接\r\n\tconst baseUrl = config.imageBaseUrl || '';\r\n\tconst path = relativePath.startsWith('/') ? relativePath.substring(1) : relativePath;\r\n\treturn `${baseUrl}/${path}`;\r\n}"], "names": ["config"], "mappings": ";;AAEO,SAAS,gBAAgB,cAAc;AAC7C,MAAI,CAAC,cAAc;AAElB,WAAO;AAAA,EACP;AAED,MAAI,aAAa,WAAW,MAAM,GAAG;AACpC,WAAO;AAAA,EACP;AAED,QAAM,UAAUA,oBAAO;AACvB,QAAM,OAAO,aAAa,WAAW,GAAG,IAAI,aAAa,UAAU,CAAC,IAAI;AACxE,SAAO,GAAG,OAAO,IAAI,IAAI;AAC1B;;"}