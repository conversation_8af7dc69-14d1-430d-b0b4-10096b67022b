{"version": 3, "file": "registration.js", "sources": ["pages_sub/pages_profile/api/data/registration.js"], "sourcesContent": ["/**\r\n * 报名相关API接口\r\n */\r\nimport http from '@/utils/request.js';\r\n\r\n/**\r\n * 获取活动的表单定义\r\n * @param {number} eventId 活动ID\r\n * @returns {Promise} API响应\r\n */\r\nexport const getFormDefinitionApi = (eventId) => {\r\n  return http.get(`/content/form-definition/event/${eventId}`);\r\n};\r\n\r\n/**\r\n * 检查用户报名状态\r\n * @param {number} eventId 活动ID\r\n * @returns {Promise} API响应\r\n */\r\nexport const checkRegistrationStatusApi = (eventId) => {\r\n  return http.get('/registration/check', { eventId });\r\n};\r\n\r\n/**\r\n * 用户报名活动 - 增强版，自动获取token并组装请求体\r\n * @param {Object} data 报名数据\r\n * @param {number} data.eventId 活动ID\r\n * @param {Object} data.formData 表单数据对象\r\n * @returns {Promise} API响应\r\n */\r\nexport const submitRegistrationApi = (data) => {\r\n  // 组装请求体 - 后端使用统一Token验证，无需手动传递token\r\n  const requestData = {\r\n    eventId: data.eventId,\r\n    formData: JSON.stringify(data.formData) // 将表单数据转换为JSON字符串\r\n  };\r\n  \r\n  return http.post('/registration/register', requestData);\r\n};\r\n\r\n/**\r\n * 用户报名活动 - 原始版本，保持向后兼容\r\n * @param {Object} data 报名数据\r\n * @param {number} data.eventId 活动ID\r\n * @param {string} data.token 用户token\r\n * @param {string} data.formData 表单数据(JSON字符串)\r\n * @returns {Promise} API响应\r\n */\r\nexport const registerEventApi = (data) => {\r\n  return http.post('/registration/register', data);\r\n};\r\n\r\n/**\r\n * 取消报名\r\n * @param {Object} data 取消报名数据\r\n * @param {number} data.eventId 活动ID\r\n * @param {string} data.token 用户token\r\n * @returns {Promise} API响应\r\n */\r\nexport const cancelRegistrationApi = (data) => {\r\n  return http.post('/registration/cancel', data);\r\n};\r\n\r\n/**\r\n * 获取用户报名记录列表\r\n * @returns {Promise} API响应\r\n */\r\nexport const getMyRegistrationsApi = () => {\r\n  return http.get('/registration/my-registrations');\r\n};\r\n\r\n/**\r\n * 获取我的报名详情 - 基于现有接口实现\r\n * @param {number} eventId 活动ID\r\n * @returns {Promise} API响应\r\n */\r\nexport const getMyRegistrationDetailApi = async (eventId) => {\r\n  try {\r\n    console.log('查找报名详情，目标 eventId:', eventId, '类型:', typeof eventId);\r\n    \r\n    // 获取所有报名记录\r\n    const response = await getMyRegistrationsApi();\r\n    \r\n    if (response.code === 200 && response.data) {\r\n      console.log('所有报名记录:', response.data);\r\n      \r\n      // 🔧 修复：处理 eventId 类型转换问题，确保比较的是相同类型\r\n      const targetEventId = Number(eventId); // 统一转换为数字类型\r\n      \r\n      // 从所有报名记录中筛选出指定活动的记录\r\n      const targetRegistration = response.data.find(item => {\r\n        const itemEventId = Number(item.eventId);\r\n        console.log('比较 eventId:', itemEventId, 'vs', targetEventId, '匹配:', itemEventId === targetEventId);\r\n        return itemEventId === targetEventId;\r\n      });\r\n      \r\n      console.log('找到的报名记录:', targetRegistration);\r\n      \r\n      if (targetRegistration) {\r\n        return {\r\n          code: 200,\r\n          data: targetRegistration,\r\n          msg: '获取成功'\r\n        };\r\n      } else {\r\n        console.warn('未找到匹配的报名记录，可能的原因:');\r\n        console.warn('- eventId 类型不匹配');\r\n        console.warn('- 用户确实未报名此活动');\r\n        console.warn('- 数据库记录异常');\r\n        \r\n        return {\r\n          code: 404,\r\n          data: null,\r\n          msg: '未找到该活动的报名记录'\r\n        };\r\n      }\r\n    } else {\r\n      console.warn('获取所有报名记录失败:', response);\r\n      return response;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取报名详情失败:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\n"], "names": ["http", "uni"], "mappings": ";;;AAUY,MAAC,uBAAuB,CAAC,YAAY;AAC/C,SAAOA,cAAAA,KAAK,IAAI,kCAAkC,OAAO,EAAE;AAC7D;AA+CY,MAAC,wBAAwB,CAAC,SAAS;AAC7C,SAAOA,mBAAK,KAAK,wBAAwB,IAAI;AAC/C;AAMY,MAAC,wBAAwB,MAAM;AACzC,SAAOA,cAAI,KAAC,IAAI,gCAAgC;AAClD;AAOY,MAAC,6BAA6B,OAAO,YAAY;AAC3D,MAAI;AACFC,wBAAY,MAAA,OAAA,0DAAA,sBAAsB,SAAS,OAAO,OAAO,OAAO;AAGhE,UAAM,WAAW,MAAM;AAEvB,QAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC1CA,oBAAY,MAAA,MAAA,OAAA,0DAAA,WAAW,SAAS,IAAI;AAGpC,YAAM,gBAAgB,OAAO,OAAO;AAGpC,YAAM,qBAAqB,SAAS,KAAK,KAAK,UAAQ;AACpD,cAAM,cAAc,OAAO,KAAK,OAAO;AACvCA,sBAAAA,6EAAY,eAAe,aAAa,MAAM,eAAe,OAAO,gBAAgB,aAAa;AACjG,eAAO,gBAAgB;AAAA,MAC/B,CAAO;AAEDA,iGAAY,YAAY,kBAAkB;AAE1C,UAAI,oBAAoB;AACtB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACf;AAAA,MACA,OAAa;AACLA,sBAAAA,MAAA,MAAA,QAAA,2DAAa,mBAAmB;AAChCA,sBAAAA,MAAa,MAAA,QAAA,2DAAA,iBAAiB;AAC9BA,sBAAAA,MAAA,MAAA,QAAA,2DAAa,cAAc;AAC3BA,sBAAAA,+EAAa,WAAW;AAExB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,QACf;AAAA,MACO;AAAA,IACP,OAAW;AACLA,oBAAA,MAAA,MAAA,QAAA,2DAAa,eAAe,QAAQ;AACpC,aAAO;AAAA,IACR;AAAA,EACF,SAAQ,OAAO;AACdA,kBAAc,MAAA,MAAA,SAAA,2DAAA,aAAa,KAAK;AAChC,UAAM;AAAA,EACP;AACH;;;;;"}