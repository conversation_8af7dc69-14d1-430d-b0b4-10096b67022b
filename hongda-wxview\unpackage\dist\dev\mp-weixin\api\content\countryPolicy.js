"use strict";
const utils_request = require("../../utils/request.js");
const BASE_URL = "/content/countryPolicy";
function listCountryPolicyArticle(query) {
  return utils_request.http.get(`${BASE_URL}/list`, query);
}
function getCountryPolicyArticle(articleId) {
  return utils_request.http.request({
    url: `${BASE_URL}/${articleId}`,
    method: "get"
  });
}
exports.getCountryPolicyArticle = getCountryPolicyArticle;
exports.listCountryPolicyArticle = listCountryPolicyArticle;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/content/countryPolicy.js.map
