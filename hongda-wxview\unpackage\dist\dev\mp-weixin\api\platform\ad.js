"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
function getAdListByPositionApi(positionCode, params = {}) {
  const queryParams = {
    positionCode,
    status: 1,
    // 只获取启用状态的广告
    pageSize: 10,
    // 默认获取10条
    ...params
  };
  common_vendor.index.__f__("log", "at api/platform/ad.js:17", "广告API请求参数:", queryParams);
  return utils_request.http.get("/ad/list", queryParams);
}
exports.getAdListByPositionApi = getAdListByPositionApi;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/platform/ad.js.map
