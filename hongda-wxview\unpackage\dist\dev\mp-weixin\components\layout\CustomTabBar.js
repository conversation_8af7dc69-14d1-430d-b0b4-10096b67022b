"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "CustomTabBar",
  props: {
    current: {
      type: Number,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const color = common_vendor.ref("#8A8A8A");
    const selectedColor = common_vendor.ref("#023F98");
    const list = common_vendor.ref([
      {
        pagePath: "/pages/index/index",
        iconPath: "/static/tabbar/recommend.png",
        selectedIconPath: "/static/tabbar/recommend_active.png",
        text: "推荐"
      },
      {
        pagePath: "/pages/article/index",
        iconPath: "/static/tabbar/article.png",
        selectedIconPath: "/static/tabbar/article_active.png",
        text: "资讯"
      },
      {
        pagePath: "/pages/event/index",
        iconPath: "/static/tabbar/event_hot.png",
        selectedIconPath: "/static/tabbar/event_hot.png",
        text: "热门活动",
        central: true
      },
      {
        pagePath: "/pages/country/index",
        iconPath: "/static/tabbar/country.png",
        selectedIconPath: "/static/tabbar/country_active.png",
        text: "国别资讯"
      },
      {
        pagePath: "/pages/profile/index",
        iconPath: "/static/tabbar/profile.png",
        selectedIconPath: "/static/tabbar/profile_active.png",
        text: "我的"
      }
    ]);
    const switchTab = (item, index) => {
      if (props.current === index) {
        return;
      }
      common_vendor.index.switchTab({
        url: item.pagePath
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(list.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.central
          }, item.central ? {} : {
            b: __props.current === index ? item.selectedIconPath : item.iconPath,
            c: common_vendor.t(item.text),
            d: __props.current === index ? selectedColor.value : color.value
          }, {
            e: item.pagePath,
            f: common_vendor.o(($event) => switchTab(item, index), item.pagePath)
          });
        }),
        b: common_assets._imports_0$8,
        c: common_vendor.o(($event) => switchTab(list.value[2], 2))
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f44adf2b"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/layout/CustomTabBar.js.map
