"use strict";
const utils_config = require("./config.js");
function getFullImageUrl(relativePath) {
  if (!relativePath) {
    return "/static/pages_event/美妆logo蓝@2x.png";
  }
  if (relativePath.startsWith("http")) {
    return relativePath;
  }
  const baseUrl = utils_config.config.imageBaseUrl;
  const path = relativePath.startsWith("/") ? relativePath.substring(1) : relativePath;
  return `${baseUrl}/${path}`;
}
exports.getFullImageUrl = getFullImageUrl;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/image.js.map
