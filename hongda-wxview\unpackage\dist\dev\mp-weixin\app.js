"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const uni_modules_uviewPlus_index = require("./uni_modules/uview-plus/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/article/index.js";
  "./pages/event/index.js";
  "./pages/country/index.js";
  "./pages/profile/index.js";
  "./pages_sub/pages_article/detail.js";
  "./pages_sub/pages_event/detail.js";
  "./pages_sub/pages_event/registration.js";
  "./pages_sub/pages_country/detail.js";
  "./pages_sub/pages_country/policy_detail.js";
  "./pages_sub/pages_profile/orders.js";
  "./pages_sub/pages_profile/contact.js";
  "./pages_sub/pages_other/search.js";
  "./pages_sub/pages_other/park_detail.js";
  "./pages_sub/pages_other/login.js";
  "./pages_sub/pages_other/registration_detail.js";
  "./pages_sub/pages_other/policy.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:8", "App Launch");
    this.loadIconFont();
    this.loadPuHuiTiFonts();
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:17", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:20", "App Hide");
  },
  methods: {
    loadIconFont() {
      common_vendor.index.loadFontFace({
        global: true,
        family: "uicon-iconfont",
        source: 'url("https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf")',
        success() {
          common_vendor.index.__f__("log", "at App.vue:30", "uview-plus图标字体加载成功");
        },
        fail(err) {
          common_vendor.index.__f__("error", "at App.vue:33", "uview-plus图标字体加载失败:", err);
          common_vendor.index.loadFontFace({
            global: true,
            family: "uicon-iconfont",
            source: 'url("https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf")',
            success() {
              common_vendor.index.__f__("log", "at App.vue:39", "备用图标字体加载成功");
            },
            fail(err2) {
              common_vendor.index.__f__("error", "at App.vue:42", "备用图标字体也加载失败:", err2);
            }
          });
        }
      });
    },
    // 动态加载阿里巴巴普惠体（统一使用公共CDN），控制台输出成败信息
    loadPuHuiTiFonts() {
      const PUHUITI_CDN_BASE = "https://cdn.jsdelivr.net/npm/@alibabafonts/puhuiti@2.0.0/fonts";
      const fontList = [
        { family: "Alibaba PuHuiTi 3.0", weight: "400", url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Regular.woff2` },
        { family: "Alibaba PuHuiTi 3.0", weight: "500", url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Medium.woff2` },
        { family: "Alibaba PuHuiTi 3.0", weight: "700", url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Bold.woff2` },
        { family: "Alibaba PuHuiTi 3.0", weight: "800", url: `${PUHUITI_CDN_BASE}/Alibaba-PuHuiTi-Heavy.woff2` }
      ];
      fontList.forEach((f) => {
        common_vendor.index.loadFontFace({
          global: true,
          family: f.family,
          // 使用公共CDN远程 HTTPS 字体 URL
          source: `url("${f.url}")`,
          desc: { weight: f.weight, style: "normal" },
          success() {
            common_vendor.index.__f__("log", "at App.vue:74", `[PuHuiTi] 加载成功 -> family: ${f.family}, weight: ${f.weight}, url: ${f.url}`);
          },
          fail(err) {
            common_vendor.index.__f__("error", "at App.vue:77", `[PuHuiTi] 加载失败 -> family: ${f.family}, weight: ${f.weight}, url: ${f.url}`, err);
          }
        });
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {};
}
const App = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
function createApp() {
  const app = common_vendor.createSSRApp(App);
  app.use(uni_modules_uviewPlus_index.uviewPlus, () => {
    return {
      options: {
        config: {
          unit: "rpx"
        },
        props: {
          // ...
        }
      }
    };
  });
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
