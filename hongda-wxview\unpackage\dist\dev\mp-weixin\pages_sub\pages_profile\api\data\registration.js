"use strict";
const common_vendor = require("../../../../common/vendor.js");
const utils_request = require("../../../../utils/request.js");
const getFormDefinitionApi = (eventId) => {
  return utils_request.http.get(`/content/form-definition/event/${eventId}`);
};
const cancelRegistrationApi = (data) => {
  return utils_request.http.post("/registration/cancel", data);
};
const getMyRegistrationsApi = () => {
  return utils_request.http.get("/registration/my-registrations");
};
const getMyRegistrationDetailApi = async (eventId) => {
  try {
    common_vendor.index.__f__("log", "at pages_sub/pages_profile/api/data/registration.js:79", "查找报名详情，目标 eventId:", eventId, "类型:", typeof eventId);
    const response = await getMyRegistrationsApi();
    if (response.code === 200 && response.data) {
      common_vendor.index.__f__("log", "at pages_sub/pages_profile/api/data/registration.js:85", "所有报名记录:", response.data);
      const targetEventId = Number(eventId);
      const targetRegistration = response.data.find((item) => {
        const itemEventId = Number(item.eventId);
        common_vendor.index.__f__("log", "at pages_sub/pages_profile/api/data/registration.js:93", "比较 eventId:", itemEventId, "vs", targetEventId, "匹配:", itemEventId === targetEventId);
        return itemEventId === targetEventId;
      });
      common_vendor.index.__f__("log", "at pages_sub/pages_profile/api/data/registration.js:97", "找到的报名记录:", targetRegistration);
      if (targetRegistration) {
        return {
          code: 200,
          data: targetRegistration,
          msg: "获取成功"
        };
      } else {
        common_vendor.index.__f__("warn", "at pages_sub/pages_profile/api/data/registration.js:106", "未找到匹配的报名记录，可能的原因:");
        common_vendor.index.__f__("warn", "at pages_sub/pages_profile/api/data/registration.js:107", "- eventId 类型不匹配");
        common_vendor.index.__f__("warn", "at pages_sub/pages_profile/api/data/registration.js:108", "- 用户确实未报名此活动");
        common_vendor.index.__f__("warn", "at pages_sub/pages_profile/api/data/registration.js:109", "- 数据库记录异常");
        return {
          code: 404,
          data: null,
          msg: "未找到该活动的报名记录"
        };
      }
    } else {
      common_vendor.index.__f__("warn", "at pages_sub/pages_profile/api/data/registration.js:118", "获取所有报名记录失败:", response);
      return response;
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at pages_sub/pages_profile/api/data/registration.js:122", "获取报名详情失败:", error);
    throw error;
  }
};
exports.cancelRegistrationApi = cancelRegistrationApi;
exports.getFormDefinitionApi = getFormDefinitionApi;
exports.getMyRegistrationDetailApi = getMyRegistrationDetailApi;
exports.getMyRegistrationsApi = getMyRegistrationsApi;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages_sub/pages_profile/api/data/registration.js.map
