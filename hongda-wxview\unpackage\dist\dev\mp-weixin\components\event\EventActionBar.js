"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Array) {
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  _easycom_up_button2();
}
const _easycom_up_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  _easycom_up_button();
}
const _sfc_main = {
  __name: "EventActionBar",
  props: {
    eventDetail: { type: Object, required: true },
    isLoading: { type: Boolean, default: false },
    registrationStatus: { type: String, required: true },
    isButtonDisabled: { type: Boolean, required: true },
    buttonText: { type: String, required: true }
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !__props.isLoading && __props.eventDetail
      }, !__props.isLoading && __props.eventDetail ? {
        b: common_assets._imports_0$12,
        c: common_vendor.o(($event) => _ctx.$emit("share")),
        d: common_vendor.p({
          customStyle: {
            width: "214rpx",
            height: "76rpx",
            margin: "0",
            padding: "0",
            border: "none",
            backgroundColor: "rgba(42, 97, 241, 0.2)",
            borderRadius: "38rpx"
          }
        }),
        e: common_vendor.t(__props.buttonText),
        f: common_vendor.o(($event) => _ctx.$emit("register")),
        g: common_vendor.p({
          type: "primary",
          disabled: __props.isButtonDisabled,
          customStyle: {
            width: "464rpx",
            height: "76rpx",
            margin: "0",
            backgroundColor: __props.registrationStatus === "registered" ? "#C4CFD1" : __props.isButtonDisabled ? "#cccccc" : "#023F98",
            borderColor: __props.registrationStatus === "registered" ? "#C4CFD1" : __props.isButtonDisabled ? "#cccccc" : "#023F98",
            borderRadius: "38rpx",
            color: "#ffffff",
            fontFamily: "Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30, sans-serif",
            fontWeight: "normal",
            fontSize: "28rpx"
          }
        })
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a955827f"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/event/EventActionBar.js.map
