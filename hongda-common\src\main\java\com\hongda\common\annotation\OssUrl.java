package com.hongda.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
<<<<<<< HEAD
 * 标记字段为阿里云OSS对象名称（objectName），用于在序列化或业务处理中生成可访问URL。
=======
 * 自定义注解，用于标记需要将OSS ObjectName转换为签名URL的字段
>>>>>>> 416f2eda4d4b229790063869cdbc556992f3cdf9
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface OssUrl {

}

