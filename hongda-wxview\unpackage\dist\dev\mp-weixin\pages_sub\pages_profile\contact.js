"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_image = require("../../utils/image.js");
const pages_sub_pages_profile_api_platform_consultant = require("./api/platform/consultant.js");
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  (_easycom_uni_icons2 + _easycom_uni_load_more2)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_uni_load_more)();
}
const navBarPaddingBottomRpx = 20;
const _sfc_main = {
  __name: "contact",
  setup(__props) {
    const navBarPaddingBottomPx = common_vendor.index.upx2px(navBarPaddingBottomRpx);
    const statusBarHeight = common_vendor.ref(0);
    const navBarHeight = common_vendor.ref(0);
    const headerHeight = common_vendor.ref(0);
    const getNavBarInfo = () => {
      try {
        const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        statusBarHeight.value = menuButtonInfo.top;
        navBarHeight.value = menuButtonInfo.height;
        headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
      } catch (e) {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = systemInfo.statusBarHeight || 20;
        navBarHeight.value = 44;
        headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack({ delta: 1 });
    };
    const loading = common_vendor.ref(true);
    const consultant = common_vendor.ref(null);
    const displayAvatarUrl = common_vendor.computed(() => {
      return consultant.value ? utils_image.getFullImageUrl(consultant.value.avatarUrl) : "";
    });
    const displayQrCodeUrl = common_vendor.computed(() => {
      return consultant.value ? utils_image.getFullImageUrl(consultant.value.qrCodeUrl) : "";
    });
    const fetchConsultantData = async () => {
      try {
        const res = await pages_sub_pages_profile_api_platform_consultant.getDisplayConsultantApi();
        if (res.code === 200 && res.data) {
          consultant.value = res.data;
        } else {
          consultant.value = null;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_profile/contact.vue:124", "获取顾问信息失败:", error);
        consultant.value = null;
      } finally {
        loading.value = false;
      }
    };
    const previewQrCode = () => {
      if (displayQrCodeUrl.value) {
        common_vendor.index.previewImage({
          urls: [displayQrCodeUrl.value]
        });
      }
    };
    const onImageError = (type) => {
      common_vendor.index.__f__("error", "at pages_sub/pages_profile/contact.vue:143", `${type} image failed to load.`);
      if (type === "avatar") {
        consultant.value.avatarUrl = "/static/images/default-avatar.png";
      } else if (type === "qrCode") {
        consultant.value.qrCodeUrl = "/static/images/default-qrcode.png";
      }
    };
    common_vendor.onLoad(() => {
      getNavBarInfo();
      loading.value = true;
      fetchConsultantData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: common_vendor.p({
          type: "left",
          color: "#000000",
          size: "22"
        }),
        c: common_vendor.o(goBack),
        d: navBarHeight.value + "px",
        e: common_vendor.unref(navBarPaddingBottomPx) + "px",
        f: headerHeight.value + "px",
        g: loading.value
      }, loading.value ? {
        h: common_vendor.p({
          status: "loading",
          ["show-icon"]: true
        })
      } : {}, {
        i: !loading.value && consultant.value
      }, !loading.value && consultant.value ? {
        j: displayAvatarUrl.value,
        k: common_vendor.o(($event) => onImageError("avatar")),
        l: common_vendor.t(consultant.value.name),
        m: common_vendor.t(consultant.value.introduction),
        n: displayQrCodeUrl.value,
        o: common_vendor.o(($event) => onImageError("qrCode")),
        p: common_vendor.o(previewQrCode)
      } : {}, {
        q: !loading.value && !consultant.value
      }, !loading.value && !consultant.value ? {
        r: common_assets._imports_0$5
      } : {}, {
        s: headerHeight.value + "px"
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d68d24cd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_profile/contact.js.map
