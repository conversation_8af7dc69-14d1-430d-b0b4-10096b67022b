"use strict";
const utils_request = require("../../utils/request.js");
function getCountryList(params) {
  const url = "/country/list";
  return utils_request.http.get(url, params);
}
function getCountryDetail(id) {
  const url = `/country/${id}`;
  return utils_request.http.get(url);
}
exports.getCountryDetail = getCountryDetail;
exports.getCountryList = getCountryList;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/content/country.js.map
