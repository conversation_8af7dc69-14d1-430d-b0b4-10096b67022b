"use strict";
const utils_request = require("../../../../utils/request.js");
const getLatestPolicyApi = (type) => {
  return utils_request.get("/policy/latest", { type });
};
const acceptPolicyApi = (policyType, policyVersion) => {
  return utils_request.post("/policy/accept", { policyType, policyVersion });
};
exports.acceptPolicyApi = acceptPolicyApi;
exports.getLatestPolicyApi = getLatestPolicyApi;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages_sub/pages_other/api/data/policy.js.map
