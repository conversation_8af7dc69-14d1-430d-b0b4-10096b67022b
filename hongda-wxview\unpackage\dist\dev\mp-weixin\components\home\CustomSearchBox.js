"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  _easycom_u_icon2();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
if (!Math) {
  _easycom_u_icon();
}
const _sfc_main = {
  __name: "CustomSearchBox",
  props: {
    // 搜索框占位符文本
    placeholder: {
      type: String,
      default: "搜索活动"
    },
    // 双向绑定的搜索值
    modelValue: {
      type: String,
      default: ""
    }
  },
  emits: ["update:modelValue", "search", "input"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const searchValue = common_vendor.ref(props.modelValue);
    common_vendor.watch(() => props.modelValue, (newValue) => {
      searchValue.value = newValue;
    });
    common_vendor.watch(searchValue, (newValue) => {
      emit("update:modelValue", newValue);
    });
    const handleSearch = (event) => {
      var _a;
      const value = ((_a = event.detail) == null ? void 0 : _a.value) || searchValue.value;
      emit("search", value);
    };
    const handleInput = (event) => {
      var _a, _b;
      const value = ((_a = event.detail) == null ? void 0 : _a.value) || ((_b = event.target) == null ? void 0 : _b.value);
      searchValue.value = value;
      emit("input", value);
    };
    const handleSearchClick = () => {
      emit("search", searchValue.value);
    };
    return (_ctx, _cache) => {
      return {
        a: __props.placeholder,
        b: common_vendor.o([($event) => searchValue.value = $event.detail.value, handleInput]),
        c: common_vendor.o(handleSearch),
        d: searchValue.value,
        e: common_vendor.p({
          name: "search",
          size: "24",
          color: "#999999"
        }),
        f: common_vendor.o(handleSearchClick)
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-395aeeb4"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
