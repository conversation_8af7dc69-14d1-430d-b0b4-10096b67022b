{"version": 3, "file": "index.js", "sources": ["pages/profile/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"my-page\">\r\n    <!-- 顶部背景和头像/登录按钮区域 -->\r\n<view class=\"header-bg\">\r\n    <!-- 背景图片 -->\r\n    <image class=\"header-bg-image\" src=\"/static/profile/mybg.png\" mode=\"aspectFill\"></image>\r\n    <view class=\"user-profile-box\">\r\n        <view class=\"avatar-container\">\r\n            <up-avatar \r\n                :size=\"54\"  \r\n                :src=\"isLoggedIn && userInfo && userInfo.avatarUrl ? userInfo.avatarUrl : '/static/profile/default-avatar.png'\"\r\n                @click=\"!isLoggedIn ? goToLogin : null\" \r\n            ></up-avatar>\r\n            <!-- 覆盖在头像上的透明按钮：微信端触发头像选择器 -->\r\n            <button\r\n              v-if=\"isLoggedIn\"\r\n              class=\"avatar-choose-btn\"\r\n              open-type=\"chooseAvatar\"\r\n              @chooseavatar=\"onChooseAvatar\"\r\n            ></button>\r\n        </view>\r\n        <view class=\"user-text-box\">\r\n            <view v-if=\"isLoggedIn\" class=\"user-info-container\">\r\n                <view class=\"username-row\">\r\n                    <text class=\"username-text\">{{ getUserDisplayName }}</text>\r\n                    <image class=\"edit-icon\" src=\"/static/profile/<EMAIL>\" mode=\"aspectFit\" @click=\"handleEditNickname\"></image>\r\n                </view>\r\n            </view>\r\n            <view v-else class=\"login-button-container\">\r\n                <text class=\"username-text\" @click=\"goToLogin\">请登录</text>\r\n            </view>\r\n        </view>\r\n    </view>\r\n\r\n    <!-- 报名订单卡片 -->\r\n    <view class=\"order-card-container\" @click=\"handleOrderCardClick\">\r\n        <!-- 背景图片 -->\r\n        <image class=\"order-card-bg-image\" src=\"/static/profile/order-card-bg.png\" mode=\"aspectFill\"></image>\r\n        <view class=\"order-card-content\">\r\n            <view class=\"order-card-left\">\r\n                <text class=\"order-card-text\">报名订单</text>\r\n            </view>\r\n            <view class=\"order-card-right\">\r\n                <view class=\"order-card-action-wrapper\">\r\n                    <text class=\"order-card-action\">查看</text>\r\n                    <image class=\"order-card-arrow\" src=\"/static/profile/Group <EMAIL>\" mode=\"aspectFit\"></image>\r\n                </view>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</view>\r\n\r\n    <!-- 简化菜单区域 -->\r\n    <view class=\"menu-list-card\">\r\n      <up-cell-group :border=\"false\">\r\n        <!-- 绑定手机号，根据登录状态显示值 -->\r\n        <up-cell title=\"绑定手机号\" :value=\"getPhoneDisplay\" :isLink=\"false\" :border=\"false\">\r\n          <template #icon>\r\n            <image class=\"menu-icon\" src=\"/static/profile/手机***********\" mode=\"aspectFit\"></image>\r\n          </template>\r\n        </up-cell>\r\n        <up-cell title=\"隐私政策\" :isLink=\"true\" arrow-direction=\"right\" :border=\"false\" @click=\"handleNavigate('/pages_sub/pages_other/policy?type=privacy_policy')\">\r\n          <template #icon>\r\n            <image class=\"menu-icon\" src=\"/static/profile/隐私***********\" mode=\"aspectFit\"></image>\r\n          </template>\r\n        </up-cell>\r\n        <up-cell title=\"用户协议\" :isLink=\"true\" arrow-direction=\"right\" :border=\"false\" @click=\"handleNavigate('/pages_sub/pages_other/policy?type=user_agreement')\">\r\n          <template #icon>\r\n            <image class=\"menu-icon\" src=\"/static/profile/协议***********\" mode=\"aspectFit\"></image>\r\n          </template>\r\n        </up-cell>\r\n        <up-cell title=\"注销账号\" :isLink=\"true\" arrow-direction=\"right\" :border=\"false\" @click=\"handleDeleteAccountClick\">\r\n          <template #icon>\r\n            <image class=\"menu-icon\" src=\"/static/profile/注销***********\" mode=\"aspectFit\"></image>\r\n          </template>\r\n        </up-cell>\r\n      </up-cell-group>\r\n    </view>\r\n\t\r\n\t<view v-if=\"isLoggedIn\" class=\"logout-button-wrapper\">\r\n\t  <view class=\"custom-logout-btn\" @click=\"logout\">\r\n\t    退出登录\r\n\t  </view>\r\n\t</view>\r\n    \r\n    <!-- 自定义底部导航栏 -->\r\n    <CustomTabBar :current=\"4\" />\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue'\r\nimport { onShow, onLoad } from '@dcloudio/uni-app'\r\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue'\r\nimport { deleteAccountApi, updateUserInfoApi } from '@/api/data/user.js'\r\nimport { BASE_URL } from '@/utils/config.js'\r\n\r\nconst MAX_NICKNAME_LENGTH = 15\r\n\r\n// 定义页面名称\r\ndefineOptions({\r\n  name: 'ProfileIndex' // 页面名称与 pages.json 路径一致\r\n})\r\n\r\n// 响应式数据\r\nconst isLoggedIn = ref(false) // 初始登录状态\r\nconst userInfo = ref(null) // 用户信息\r\n\r\n// 计算属性\r\nconst getUserDisplayName = computed(() => {\r\n  if (!userInfo.value) {\r\n    return '用户' // 默认显示\r\n  }\r\n  \r\n  // 优先显示昵称，其次显示手机号，最后显示默认名称\r\n  if (userInfo.value.nickname) {\r\n    return userInfo.value.nickname\r\n  }\r\n  \r\n  if (userInfo.value.phoneNumber) {\r\n    // 如果有手机号，显示格式化的手机号\r\n    const phone = userInfo.value.phoneNumber\r\n    if (phone.length === 11) {\r\n      return phone.substring(0, 3) + '****' + phone.substring(7)\r\n    }\r\n    return phone\r\n  }\r\n  \r\n  return '用户' // 默认显示\r\n})\r\n\r\nconst getPhoneDisplay = computed(() => {\r\n  if (!isLoggedIn.value) {\r\n    return '' // 未登录时不显示文案\r\n  }\r\n  \r\n  // 检查手机号字段（可能是phone或phoneNumber）\r\n  const phone = userInfo.value?.phone || userInfo.value?.phoneNumber\r\n  \r\n  if (!phone) {\r\n    return '未绑定' // 已登录但没有手机号\r\n  }\r\n  \r\n  // 格式化手机号显示（中间4位用*号替换）\r\n  if (phone.length === 11) {\r\n    return phone.substring(0, 3) + '****' + phone.substring(7)\r\n  }\r\n  \r\n  // 如果手机号格式不标准，直接显示\r\n  return phone\r\n})\r\n\r\n// 方法定义\r\n// 统一的导航守卫方法\r\nconst handleNavigate = (url) => {\r\n  // 检查登录状态\r\n  if (!isLoggedIn.value) {\r\n    // 未登录时，跳转到登录页\r\n    console.log('用户未登录，跳转到登录页')\r\n    goToLogin()\r\n    return\r\n  }\r\n  \r\n  // 已登录时，正常跳转到目标页面\r\n  console.log('用户已登录，跳转到:', url)\r\n  uni.navigateTo({\r\n    url: url,\r\n    fail: (err) => {\r\n      console.error('页面跳转失败:', err)\r\n      uni.showToast({\r\n        title: '页面跳转失败',\r\n        icon: 'none',\r\n        duration: 2000\r\n      })\r\n    }\r\n  })\r\n}\r\n\r\n// 报名订单卡片点击处理\r\nconst handleOrderCardClick = () => {\r\n  if (!isLoggedIn.value) {\r\n    console.log('点击报名订单卡片，需要先登录')\r\n    goToLogin()\r\n  } else {\r\n    console.log('点击报名订单卡片，跳转到订单页面')\r\n    uni.navigateTo({\r\n      url: '/pages_sub/pages_profile/orders',\r\n      fail: (err) => {\r\n        console.error('跳转订单页面失败:', err)\r\n        uni.showToast({\r\n          title: '页面跳转失败',\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n      }\r\n    })\r\n  }\r\n}\r\n\r\n\r\n// 注销账号点击处理\r\nconst handleDeleteAccountClick = () => {\r\n  if (!isLoggedIn.value) {\r\n    console.log('注销账号需要先登录')\r\n    goToLogin()\r\n    return\r\n  }\r\n  \r\n  // 已登录时，显示注销确认弹窗\r\n  uni.showModal({\r\n    title: '注销账号',\r\n    content: '注销后账号将被标记为已注销，确定要继续吗？',\r\n    confirmText: '确定注销',\r\n    cancelText: '取消',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        // 用户确认注销，调用注销方法\r\n        console.log('用户确认注销账号')\r\n        confirmDeleteAccount()\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\n// 确认注销账号的实际逻辑\r\nconst confirmDeleteAccount = async () => {\r\n  console.log('=== 开始执行注销账号流程 ===')\r\n  \r\n  // 显示加载提示\r\n  uni.showLoading({ title: '正在注销...' })\r\n  \r\n  try {\r\n    console.log('调用后端注销接口...')\r\n    const result = await deleteAccountApi()\r\n    \r\n    console.log('注销接口调用成功:', result)\r\n    \r\n    // 关闭加载提示\r\n    uni.hideLoading()\r\n    \r\n    // 显示成功提示\r\n    uni.showToast({\r\n      title: '账号已成功注销',\r\n      icon: 'success',\r\n      duration: 2000\r\n    })\r\n    \r\n    // 清空本地数据但不显示额外提示（注销成功提示已经显示过了）\r\n    clearUserDataSilently()\r\n    \r\n    console.log('注销账号流程完成')\r\n    \r\n  } catch (error) {\r\n    console.error('注销账号过程中发生错误:', error)\r\n    \r\n    // 关闭加载提示\r\n    uni.hideLoading()\r\n    \r\n    // 显示错误提示\r\n    uni.showToast({\r\n      title: '注销失败，请稍后再试',\r\n      icon: 'none',\r\n      duration: 3000\r\n    })\r\n    \r\n    // 显示详细错误信息（开发调试用）\r\n    console.error('注销失败详情:', {\r\n      message: error.message,\r\n      stack: error.stack\r\n    })\r\n  }\r\n}\r\n\r\n// 检查登录状态的方法\r\nconst checkLoginStatus = () => {\r\n  // 检查实际的token和用户信息\r\n  const token = uni.getStorageSync('token')\r\n  const userInfoData = uni.getStorageSync('userInfo')\r\n  \r\n  console.log('从本地存储获取的token:', token)\r\n  console.log('从本地存储获取的userInfo:', userInfoData)\r\n  \r\n  const newLoginStatus = !!token\r\n  console.log('计算出的登录状态:', newLoginStatus)\r\n  console.log('当前页面登录状态:', isLoggedIn.value)\r\n  \r\n  // 强制更新登录状态和用户信息\r\n  isLoggedIn.value = newLoginStatus\r\n  userInfo.value = userInfoData || null\r\n  \r\n  if (isLoggedIn.value && userInfoData) {\r\n    // 如果已登录且有用户信息，可以在这里设置用户数据\r\n    console.log('用户已登录，用户信息:', userInfoData)\r\n    if (userInfoData.phoneNumber) {\r\n      console.log('用户手机号:', userInfoData.phoneNumber)\r\n    }\r\n  } else if (isLoggedIn.value && !userInfoData) {\r\n    console.log('有token但无用户信息')\r\n  } else {\r\n    console.log('用户未登录')\r\n  }\r\n}\r\n\r\n// 点击\"请登录\"按钮跳转到登录页面\r\nconst goToLogin = () => {\r\n  uni.navigateTo({\r\n    url: '/pages_sub/pages_other/login'\r\n  })\r\n}\r\n\r\n// 点击\"退出登录\"按钮\r\nconst logout = (isDeleteAccount = false) => {\r\n  // 如果是注销账号操作，直接清除数据，不显示确认弹窗\r\n  if (isDeleteAccount) {\r\n    clearUserData('已退出登录')\r\n    return\r\n  }\r\n  \r\n  // 正常退出登录时显示确认弹窗\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确定要退出登录吗？',\r\n    success: (res) => {\r\n      if (res.confirm) {\r\n        clearUserData('已退出登录')\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\n// 编辑昵称点击处理\r\nconst handleEditNickname = () => {\r\n  if (!isLoggedIn.value) {\r\n    console.log('编辑昵称需要先登录')\r\n    goToLogin()\r\n    return\r\n  }\r\n  \r\n  // 弹出输入框让用户输入新昵称\r\n  uni.showModal({\r\n    title: '修改昵称',\r\n    editable: true,\r\n    placeholderText: '请输入新昵称',\r\n    content: userInfo.value?.nickname || '',\r\n    success: async (res) => {\r\n      if (res.confirm && res.content && res.content.trim()) {\r\n        const newNickname = res.content.trim()\r\n        \r\n        // 检查昵称长度\r\n        if (newNickname.length > MAX_NICKNAME_LENGTH) {\r\n          uni.showToast({\r\n            title: `昵称不能超过${MAX_NICKNAME_LENGTH}个字符`,\r\n            icon: 'none',\r\n            duration: 2000\r\n          })\r\n          return\r\n        }\r\n        \r\n        // 调用API更新昵称\r\n        await updateNickname(newNickname)\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\n// 更新昵称的方法\r\nconst updateNickname = async (newNickname) => {\r\n  console.log('=== 开始更新昵称 ===')\r\n\r\n  // 再次校验长度，双重保障\r\n  if (!newNickname || newNickname.trim().length === 0) {\r\n    uni.showToast({\r\n      title: '昵称不能为空',\r\n      icon: 'none',\r\n      duration: 2000\r\n    })\r\n    return\r\n  }\r\n  if (newNickname.length > MAX_NICKNAME_LENGTH) {\r\n    uni.showToast({\r\n      title: `昵称不能超过${MAX_NICKNAME_LENGTH}个字符`,\r\n      icon: 'none',\r\n      duration: 2000\r\n    })\r\n    return\r\n  }\r\n  \r\n  // 显示加载提示\r\n  uni.showLoading({ title: '正在更新...' })\r\n  \r\n  try {\r\n    console.log('调用updateUserInfoApi更新昵称:', newNickname)\r\n    const result = await updateUserInfoApi({\r\n      nickname: newNickname\r\n    })\r\n    \r\n    console.log('昵称更新接口调用成功:', result)\r\n    \r\n    // 关闭加载提示\r\n    uni.hideLoading()\r\n    \r\n    // 更新本地用户信息\r\n    if (userInfo.value) {\r\n      userInfo.value.nickname = newNickname\r\n      // 同时更新本地存储\r\n      uni.setStorageSync('userInfo', userInfo.value)\r\n    }\r\n    \r\n    // 显示成功提示\r\n    uni.showToast({\r\n      title: '昵称修改成功',\r\n      icon: 'success',\r\n      duration: 2000\r\n    })\r\n    \r\n    console.log('昵称更新完成')\r\n    \r\n  } catch (error) {\r\n    console.error('更新昵称过程中发生错误:', error)\r\n    \r\n    // 关闭加载提示\r\n    uni.hideLoading()\r\n    \r\n    // 显示错误提示\r\n    uni.showToast({\r\n      title: error.message || '更新失败，请稍后再试',\r\n      icon: 'none',\r\n      duration: 3000\r\n    })\r\n    \r\n    console.error('昵称更新失败详情:', {\r\n      message: error.message,\r\n      stack: error.stack\r\n    })\r\n  }\r\n}\r\n\r\n// 清除用户数据的统一方法\r\nconst clearUserData = (message) => {\r\n  console.log('=== 开始清除用户数据 ===')\r\n  \r\n  // 清除token和用户信息\r\n  uni.removeStorageSync('token')\r\n  uni.removeStorageSync('userInfo')\r\n  \r\n  // 更新页面状态\r\n  isLoggedIn.value = false\r\n  userInfo.value = null\r\n  \r\n  // 显示提示信息\r\n  if (message) {\r\n    uni.showToast({\r\n      title: message,\r\n      icon: 'success',\r\n      duration: 1500\r\n    })\r\n  }\r\n  \r\n  console.log('用户数据已清除，UI已更新')\r\n}\r\n\r\n// 静默清除用户数据（不显示提示）\r\nconst clearUserDataSilently = () => {\r\n  console.log('=== 静默清除用户数据 ===')\r\n  \r\n  // 清除token和用户信息\r\n  uni.removeStorageSync('token')\r\n  uni.removeStorageSync('userInfo')\r\n  \r\n  // 更新页面状态\r\n  isLoggedIn.value = false\r\n  userInfo.value = null\r\n  \r\n  console.log('用户数据已静默清除，UI已更新')\r\n}\r\n\r\n// 已移除模拟登录相关逻辑\r\n\r\n// 生命周期钩子\r\nonShow(() => {\r\n  // 页面显示时隐藏原生 tabBar\r\n  uni.hideTabBar();\r\n  // 添加小延迟确保数据已保存\r\n  setTimeout(() => {\r\n    checkLoginStatus()\r\n  }, 100)\r\n})\r\n\r\nonLoad(() => {\r\n  checkLoginStatus()\r\n})\r\n\r\n// 选择头像回调（仅微信端可用）\r\nconst onChooseAvatar = (e) => {\r\n  const tempPath = e?.detail?.avatarUrl\r\n  if (!tempPath) return\r\n  uploadAvatar(tempPath)\r\n}\r\n\r\n// 上传并保存头像\r\nconst uploadAvatar = async (filePath) => {\r\n  if (!isLoggedIn.value) {\r\n    goToLogin()\r\n    return\r\n  }\r\n  const token = uni.getStorageSync('token')\r\n  if (!token) {\r\n    goToLogin()\r\n    return\r\n  }\r\n\r\n  uni.showLoading({ title: '上传中...' })\r\n  try {\r\n    await new Promise((resolve, reject) => {\r\n      uni.uploadFile({\r\n        url: BASE_URL + '/common/upload',\r\n        filePath,\r\n        name: 'file',\r\n        header: {\r\n          Authorization: 'Bearer ' + token\r\n        },\r\n        success: async (res) => {\r\n          try {\r\n            const data = JSON.parse(res.data || '{}')\r\n            if (res.statusCode === 200 && data.code === 200 && data.url) {\r\n              const url = data.url\r\n              await updateUserInfoApi({ avatarUrl: url })\r\n              if (userInfo.value) {\r\n                userInfo.value.avatarUrl = url\r\n                uni.setStorageSync('userInfo', userInfo.value)\r\n              }\r\n              uni.showToast({ title: '头像已更新', icon: 'success' })\r\n              resolve(true)\r\n            } else {\r\n              uni.showToast({ title: '上传失败', icon: 'none' })\r\n              reject(new Error('upload error'))\r\n            }\r\n          } catch (err) {\r\n            uni.showToast({ title: '响应解析失败', icon: 'none' })\r\n            reject(err)\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          uni.showToast({ title: '上传失败', icon: 'none' })\r\n          reject(err)\r\n        },\r\n        complete: () => {\r\n          uni.hideLoading()\r\n        }\r\n      })\r\n    })\r\n  } catch (e) {\r\n    // 已提示\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.my-page {\r\n  background-color: #F5F5F5;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* --- 顶部区域 --- */\r\n.header-bg {\r\n    position: relative;\r\n    height: 452rpx;\r\n}\r\n\r\n.header-bg-image {\r\n    position: absolute;\r\n    top:0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 452rpx;\r\n    z-index: 1;\r\n}\r\n\r\n.user-profile-box {\r\n    position: absolute;\r\n    /* 使用 calc() 动态计算正确的垂直位置 */\r\n    top: calc(186rpx + var(--status-bar-height));\r\n    /* 使用我们确认的左边距 */\r\n    left: 32rpx;\r\n    right: 40rpx; /* 增加一个右边距，防止编辑按钮贴边 */\r\n    z-index: 2;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n}\r\n\r\n/* 覆盖在头像上的透明按钮，保持可点击 */\r\n.avatar-choose-btn {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 54px;\r\n  height: 54px;\r\n  background: transparent;\r\n  border: none;\r\n  padding: 0;\r\n  opacity: 0;\r\n}\r\n\r\n.user-text-box {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-left: 20rpx;\r\n    color: #ffffff;\r\n}\r\n\r\n.user-info-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n}\r\n\r\n.username-row {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.username-text {\r\n    max-width: 200rpx;\r\n    height: 52rpx;\r\n    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\r\n    font-weight: normal;\r\n    font-size: 36rpx;\r\n    color: #FFFFFF;\r\n    line-height: 52rpx;\r\n    text-align: left;\r\n    font-style: normal;\r\n    text-transform: none;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n.edit-icon-box {\r\n    display: none;\r\n}\r\n\r\n.edit-icon {\r\n    width: 28rpx;\r\n    height: 28rpx;\r\n    margin-left: 16rpx;\r\n}\r\n\r\n/* --- 报名订单卡片 --- */\r\n.order-card-container {\r\n    position: absolute;\r\n    top: calc(348rpx + var(--status-bar-height));\r\n    left: 24rpx;\r\n    right: 24rpx;\r\n    height: 116rpx;\r\n    box-sizing: border-box;\r\n    z-index: 3;\r\n    margin: 0;\r\n    border-radius: 16rpx;\r\n    overflow: hidden;\r\n}\r\n\r\n.order-card-bg-image {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 1;\r\n    border-radius: 16rpx;\r\n}\r\n\r\n.order-card-content {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 32rpx;\r\n    height: 100%;\r\n    z-index: 2;\r\n}\r\n\r\n.order-card-left {\r\n\tmargin-left:68rpx;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.order-card-text {\r\n    color: #8C5E2D;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    margin-left: 16rpx;\r\n}\r\n\r\n.order-card-right {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.order-card-action-wrapper {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 116rpx;\r\n    height: 40rpx;\r\n    border-radius: 20rpx 20rpx 20rpx 20rpx;\r\n    border: 1rpx solid #E69D3A;\r\n    background-color: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.order-card-action {\r\n    width: 48rpx;\r\n    height: 36rpx;\r\n    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\r\n    font-weight: normal;\r\n    font-size: 24rpx;\r\n    color: #452D03;\r\n    line-height: 36rpx;\r\n    text-align: left;\r\n    font-style: normal;\r\n    text-transform: none;\r\n    margin-right: 4rpx;\r\n}\r\n\r\n.order-card-arrow {\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n}\r\n\r\n/* --- 白色列表 --- */\r\n.menu-list-card {\r\n    background-color: #ffffff;\r\n    margin: 92rpx 24rpx 0 24rpx;\r\n    border-radius: 20rpx;\r\n    padding: 10rpx 0;\r\n    position: relative;\r\n    z-index: 2;\r\n}\r\n\r\n.menu-icon {\r\n    width: 48rpx;\r\n    height: 48rpx;\r\n    margin-right: 20rpx;\r\n}\r\n\r\n.logout-button-wrapper {\r\n  padding: 24rpx 0;\r\n  margin: 0 24rpx; \r\n}\r\n\r\n.custom-logout-btn {\r\n  width: 702rpx;\r\n  height: 76rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  \r\n  &:active {\r\n    background-color: #f5f5f5;\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/profile/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "deleteAccountApi", "updateUserInfoApi", "onShow", "onLoad", "BASE_URL"], "mappings": ";;;;;;;;;;;;;;;;;AA6FA,MAAM,eAAe,MAAW;AAIhC,MAAM,sBAAsB;;;;;;;AAQ5B,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,WAAWA,cAAG,IAAC,IAAI;AAGzB,UAAM,qBAAqBC,cAAQ,SAAC,MAAM;AACxC,UAAI,CAAC,SAAS,OAAO;AACnB,eAAO;AAAA,MACR;AAGD,UAAI,SAAS,MAAM,UAAU;AAC3B,eAAO,SAAS,MAAM;AAAA,MACvB;AAED,UAAI,SAAS,MAAM,aAAa;AAE9B,cAAM,QAAQ,SAAS,MAAM;AAC7B,YAAI,MAAM,WAAW,IAAI;AACvB,iBAAO,MAAM,UAAU,GAAG,CAAC,IAAI,SAAS,MAAM,UAAU,CAAC;AAAA,QAC1D;AACD,eAAO;AAAA,MACR;AAED,aAAO;AAAA,IACT,CAAC;AAED,UAAM,kBAAkBA,cAAQ,SAAC,MAAM;;AACrC,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO;AAAA,MACR;AAGD,YAAM,UAAQ,cAAS,UAAT,mBAAgB,YAAS,cAAS,UAAT,mBAAgB;AAEvD,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACR;AAGD,UAAI,MAAM,WAAW,IAAI;AACvB,eAAO,MAAM,UAAU,GAAG,CAAC,IAAI,SAAS,MAAM,UAAU,CAAC;AAAA,MAC1D;AAGD,aAAO;AAAA,IACT,CAAC;AAID,UAAM,iBAAiB,CAAC,QAAQ;AAE9B,UAAI,CAAC,WAAW,OAAO;AAErBC,sBAAAA,qDAAY,cAAc;AAC1B,kBAAW;AACX;AAAA,MACD;AAGDA,oBAAAA,qDAAY,cAAc,GAAG;AAC7BA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,QACA,MAAM,CAAC,QAAQ;AACbA,wBAAAA,MAAc,MAAA,SAAA,kCAAA,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,uBAAuB,MAAM;AACjC,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,gBAAgB;AAC5B,kBAAW;AAAA,MACf,OAAS;AACLA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,kBAAkB;AAC9BA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,UACL,MAAM,CAAC,QAAQ;AACbA,0BAAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,GAAG;AAC9BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACpB,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH;AAIA,UAAM,2BAA2B,MAAM;AACrC,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAY,MAAA,OAAA,kCAAA,WAAW;AACvB,kBAAW;AACX;AAAA,MACD;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAY,MAAA,OAAA,kCAAA,UAAU;AACtB,iCAAsB;AAAA,UACvB;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,uBAAuB,YAAY;AACvCA,oBAAAA,qDAAY,oBAAoB;AAGhCA,oBAAAA,MAAI,YAAY,EAAE,OAAO,UAAS,CAAE;AAEpC,UAAI;AACFA,sBAAAA,qDAAY,aAAa;AACzB,cAAM,SAAS,MAAMC,+BAAkB;AAEvCD,sBAAAA,MAAY,MAAA,OAAA,kCAAA,aAAa,MAAM;AAG/BA,sBAAAA,MAAI,YAAa;AAGjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAGD,8BAAuB;AAEvBA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,UAAU;AAAA,MAEvB,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,gBAAgB,KAAK;AAGnCA,sBAAAA,MAAI,YAAa;AAGjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAGDA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,WAAW;AAAA,UACvB,SAAS,MAAM;AAAA,UACf,OAAO,MAAM;AAAA,QACnB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAE7B,YAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAM,eAAeA,cAAAA,MAAI,eAAe,UAAU;AAElDA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,kBAAkB,KAAK;AACnCA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,qBAAqB,YAAY;AAE7C,YAAM,iBAAiB,CAAC,CAAC;AACzBA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,cAAc;AACvCA,oBAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,WAAW,KAAK;AAGzC,iBAAW,QAAQ;AACnB,eAAS,QAAQ,gBAAgB;AAEjC,UAAI,WAAW,SAAS,cAAc;AAEpCA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,eAAe,YAAY;AACvC,YAAI,aAAa,aAAa;AAC5BA,wBAAA,MAAA,MAAA,OAAA,kCAAY,UAAU,aAAa,WAAW;AAAA,QAC/C;AAAA,MACF,WAAU,WAAW,SAAS,CAAC,cAAc;AAC5CA,sBAAAA,qDAAY,cAAc;AAAA,MAC9B,OAAS;AACLA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,OAAO;AAAA,MACpB;AAAA,IACH;AAGA,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,SAAS,CAAC,kBAAkB,UAAU;AAE1C,UAAI,iBAAiB;AACnB,sBAAc,OAAO;AACrB;AAAA,MACD;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,0BAAc,OAAO;AAAA,UACtB;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;;AAC/B,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAY,MAAA,OAAA,kCAAA,WAAW;AACvB,kBAAW;AACX;AAAA,MACD;AAGDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,WAAS,cAAS,UAAT,mBAAgB,aAAY;AAAA,QACrC,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,WAAW,IAAI,WAAW,IAAI,QAAQ,QAAQ;AACpD,kBAAM,cAAc,IAAI,QAAQ,KAAM;AAGtC,gBAAI,YAAY,SAAS,qBAAqB;AAC5CA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO,SAAS,mBAAmB;AAAA,gBACnC,MAAM;AAAA,gBACN,UAAU;AAAA,cACtB,CAAW;AACD;AAAA,YACD;AAGD,kBAAM,eAAe,WAAW;AAAA,UACjC;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,OAAO,gBAAgB;AAC5CA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,gBAAgB;AAG5B,UAAI,CAAC,eAAe,YAAY,KAAI,EAAG,WAAW,GAAG;AACnDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AACD,UAAI,YAAY,SAAS,qBAAqB;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,SAAS,mBAAmB;AAAA,UACnC,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AAGDA,oBAAAA,MAAI,YAAY,EAAE,OAAO,UAAS,CAAE;AAEpC,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,4BAA4B,WAAW;AACnD,cAAM,SAAS,MAAME,gCAAkB;AAAA,UACrC,UAAU;AAAA,QAChB,CAAK;AAEDF,sBAAAA,MAAY,MAAA,OAAA,kCAAA,eAAe,MAAM;AAGjCA,sBAAAA,MAAI,YAAa;AAGjB,YAAI,SAAS,OAAO;AAClB,mBAAS,MAAM,WAAW;AAE1BA,wBAAAA,MAAI,eAAe,YAAY,SAAS,KAAK;AAAA,QAC9C;AAGDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAEDA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,QAAQ;AAAA,MAErB,SAAQ,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,gBAAgB,KAAK;AAGnCA,sBAAAA,MAAI,YAAa;AAGjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAEDA,sBAAAA,uDAAc,aAAa;AAAA,UACzB,SAAS,MAAM;AAAA,UACf,OAAO,MAAM;AAAA,QACnB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,YAAY;AACjCA,oBAAAA,qDAAY,kBAAkB;AAG9BA,oBAAG,MAAC,kBAAkB,OAAO;AAC7BA,oBAAG,MAAC,kBAAkB,UAAU;AAGhC,iBAAW,QAAQ;AACnB,eAAS,QAAQ;AAGjB,UAAI,SAAS;AACXA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAEDA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,eAAe;AAAA,IAC7B;AAGA,UAAM,wBAAwB,MAAM;AAClCA,oBAAAA,qDAAY,kBAAkB;AAG9BA,oBAAG,MAAC,kBAAkB,OAAO;AAC7BA,oBAAG,MAAC,kBAAkB,UAAU;AAGhC,iBAAW,QAAQ;AACnB,eAAS,QAAQ;AAEjBA,oBAAAA,qDAAY,iBAAiB;AAAA,IAC/B;AAKAG,kBAAAA,OAAO,MAAM;AAEXH,oBAAG,MAAC,WAAU;AAEd,iBAAW,MAAM;AACf,yBAAkB;AAAA,MACnB,GAAE,GAAG;AAAA,IACR,CAAC;AAEDI,kBAAAA,OAAO,MAAM;AACX,uBAAkB;AAAA,IACpB,CAAC;AAGD,UAAM,iBAAiB,CAAC,MAAM;;AAC5B,YAAM,YAAW,4BAAG,WAAH,mBAAW;AAC5B,UAAI,CAAC;AAAU;AACf,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,eAAe,OAAO,aAAa;AACvC,UAAI,CAAC,WAAW,OAAO;AACrB,kBAAW;AACX;AAAA,MACD;AACD,YAAM,QAAQJ,cAAAA,MAAI,eAAe,OAAO;AACxC,UAAI,CAAC,OAAO;AACV,kBAAW;AACX;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAQ,CAAE;AACnC,UAAI;AACF,cAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrCA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAKK,aAAQ,WAAG;AAAA,YAChB;AAAA,YACA,MAAM;AAAA,YACN,QAAQ;AAAA,cACN,eAAe,YAAY;AAAA,YAC5B;AAAA,YACD,SAAS,OAAO,QAAQ;AACtB,kBAAI;AACF,sBAAM,OAAO,KAAK,MAAM,IAAI,QAAQ,IAAI;AACxC,oBAAI,IAAI,eAAe,OAAO,KAAK,SAAS,OAAO,KAAK,KAAK;AAC3D,wBAAM,MAAM,KAAK;AACjB,wBAAMH,gCAAkB,EAAE,WAAW,KAAK;AAC1C,sBAAI,SAAS,OAAO;AAClB,6BAAS,MAAM,YAAY;AAC3BF,kCAAAA,MAAI,eAAe,YAAY,SAAS,KAAK;AAAA,kBAC9C;AACDA,gCAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,WAAW;AACjD,0BAAQ,IAAI;AAAA,gBAC1B,OAAmB;AACLA,gCAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C,yBAAO,IAAI,MAAM,cAAc,CAAC;AAAA,gBACjC;AAAA,cACF,SAAQ,KAAK;AACZA,8BAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,QAAQ;AAC/C,uBAAO,GAAG;AAAA,cACX;AAAA,YACF;AAAA,YACD,MAAM,CAAC,QAAQ;AACbA,4BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C,qBAAO,GAAG;AAAA,YACX;AAAA,YACD,UAAU,MAAM;AACdA,4BAAAA,MAAI,YAAa;AAAA,YAClB;AAAA,UACT,CAAO;AAAA,QACP,CAAK;AAAA,MACF,SAAQ,GAAG;AAAA,MAEX;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACziBA,GAAG,WAAW,eAAe;"}